import os
import time
import re
import json
import logging
import sys
import argparse
import traceback
from datetime import datetime
from tqdm import tqdm
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, StaleElementReferenceException, NoSuchElementException

# 更新记录:
# 2025-05-01: 修复了非K线数据类型(如bookDepth, metrics)被错误处理为K线数据的问题
# - 修改extract_pairs_from_html函数，处理data_type为列表的情况
# - 修改get_data_files_with_visible_browser函数，处理data_type为列表的情况
# - 修改main函数中判断是否是K线数据的逻辑
# - 修改补充爬取部分，根据数据类型决定是否传递时间周期参数

# 设置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"crawler_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 屏蔽selenium内部日志
logging.getLogger('selenium').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)

# Chrome浏览器路径
CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

# 基础URL
BASE_URL = "https://data.binance.vision/"

# 市场类型常量
MARKET_SPOT = "spot"  # 现货
MARKET_FUTURES_UM = "futures/um"  # U本位合约
MARKET_FUTURES_CM = "futures/cm"  # 币本位合约
MARKET_OPTION = "option" #期权

# 数据类型常量
DATA_KLINES = "klines"  # K线数据
DATA_TRADES = "trades"  # 交易数据
DATA_AGGRADES = "aggTrades"  # 聚合交易数据

DATA_BOOKTICKER = "bookTicker" #合约only，最优挂单
DATA_INDEXPRICE = "indexPriceKlines"  #合约only，价格指数K线
DATA_MARKPRICE = "markPriceKlines" #合约only，标记价格K线
DATA_PREMIUMINDEX = "premiumIndexKlines" #合约only，溢价指数K线

DATA_FUNDING = "fundingRate" #月度合约only，资金费率

DATA_DEPTH = "bookDepth"  # 日度合约only，订单深度
DATA_METRICS = "metrics" #日度合约only，合约指标

DATA_LIQUIDATION = "liquidationSnapshot" #日度cm合约only，爆仓清算快照

DATA_BVOL = "BVOLIndex" #期权only，波动率指数，只有比特币和以太坊的数据
DATA_EOH = "EOHSummary" #期权only，期权EOH指数K线(2013.10之后就不更新了）


# 时间周期常量 (仅适用于K线数据)
INTERVAL_1s = "1s"  # 1秒（不全）
INTERVAL_1m = "1m"  # 1分钟
INTERVAL_3m = "3m"  # 3分钟
INTERVAL_5m = "5m"  # 5分钟
INTERVAL_15m = "15m"  # 15分钟
INTERVAL_30m = "30m"  # 30分钟
INTERVAL_1h = "1h"  # 1小时
INTERVAL_2h = "2h"  # 2小时
INTERVAL_4h = "4h"  # 4小时
INTERVAL_6h = "6h"  # 6小时
INTERVAL_8h = "8h"  # 8小时
INTERVAL_12h = "12h"  # 12小时
INTERVAL_1d = "1d"  # 1天

INTERVAL_3d = "3d"  # 3天(月度数据only
INTERVAL_1w = "1w"  # 1周（月度数据only）
INTERVAL_1mo = "1mo"  # 1月（月度数据only）


# 数据频率常量
FREQ_DAILY = "daily"       # 每日数据
FREQ_MONTHLY = "monthly"   # 月度数据

# 默认配置
DEFAULT_CONFIG = {
    "data_frequency": FREQ_DAILY,  # 可选日度FREQ_DAILY, 月度FREQ_ MONTHLY
    "market_type": MARKET_FUTURES_CM,  # 主要是现货MARKET_SPOT, U本位合约MARKET_FUTURES_UM, 币本位合约MARKET_FUTURES_CM, 少量期权MARKET_OPTION
    "data_type": DATA_MARKPRICE,  
    #-----------注意数据类型的适配度，很多数据只有现货or合约，月度or日度的-----------
    #全适用的有（3个）：K线数据DATA_KLINES, 交易数据DATA_TRADES, 聚合交易数据DATA_AGGRADES
    #合约only但日度月度均适用（4个）：最优挂单DATA_BOOKTICKER, 价格指数K线DATA_INDEXPRICE, 标记价格K线DATA_MARKPRICE, 溢价指数K线DATA_PREMIUMINDEX
    #月度合约only（1个）：资金费率DATA_FUNDING
    #日度合约only（2个）：订单深度DATA_DEPTH, 合约指标DATA_METRICS
    #日度币本位合约only（1个）：爆仓清算快照DATA_LIQUIDATION
    #期权only（2个）：波动率指数DATA_BVOL, 期权EOH指数K线DATA_EOH（已经很久不更新了）  
    "intervals": [INTERVAL_1d],  # 默认为1天周期，主要适用K线数据，其他数据类型不适用
    #全适用时间周期有（13个）：1s, 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d
    #月度适用时间周期有（3个）：3d, 1w, 1mo

    "multi_data_types": True,  # Flase为单数据类型爬取，True为多数据类型爬取；如果为True，则data_type必须为列表，否则会报错
    "data_types_list": [DATA_DEPTH, DATA_METRICS],  # 如果要爬取的多种数据类型列表，注意必须是同样频率的，比如都是日度或者都是月度
    #以下基本不用动
    "output_dir": "binance_links",  # 不用动，默认输出目录
    "chrome_path": r"C:\Program Files\Google\Chrome\Application\chrome.exe",  # 电脑中Chrome安装路径

}

# 保存链接的目录
output_dir = DEFAULT_CONFIG["output_dir"]
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Chrome浏览器路径
CHROME_PATH = DEFAULT_CONFIG["chrome_path"]

# 最大重试次数
MAX_RETRIES = 5
# 页面加载超时时间(秒)
PAGE_LOAD_TIMEOUT = 120

# 全局配置变量
config = DEFAULT_CONFIG.copy()

def setup_visible_browser():
    """设置可见的Chrome浏览器"""
    chrome_options = Options()
    chrome_options.binary_location = CHROME_PATH

    # 不使用无头模式，让浏览器可见
    # 添加其他必要选项
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--start-maximized")

    # 禁用日志输出到控制台，避免USB设备等警告
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

    # 创建WebDriver
    logger.info("启动Chrome浏览器...")
    driver = webdriver.Chrome(options=chrome_options)
    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    return driver

def load_page_with_retry(driver, url, max_retries=MAX_RETRIES):
    """带重试机制的页面加载"""
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试加载页面 (尝试 {attempt+1}/{max_retries}): {url}")
            driver.get(url)

            # 确保页面完全加载
            wait_for_page_load(driver)

            # 检查页面是否有内容
            page_html = driver.execute_script("return document.documentElement.outerHTML;")
            page_length = len(page_html)
            logger.info(f"页面已加载，HTML长度: {page_length} 字符")
            
            # 只有页面内容太小时才等待额外时间
            if page_length < 1000:
                logger.info("页面内容较少，等待5秒确保完全加载...")
                time.sleep(5)
            else:
                logger.info("页面内容已加载，继续处理...")

            return True
        except (TimeoutException, WebDriverException) as e:
            if attempt < max_retries - 1:
                logger.warning(f"页面加载超时或出错: {e}")
                logger.info(f"等待3秒后重试...")
                time.sleep(3)
            else:
                logger.error(f"页面加载失败，已达到最大重试次数: {url}")
                return False
    return False

def wait_for_page_load(driver, timeout=60):
    """等待页面完全加载"""
    start_time = time.time()
    last_page_source = ""
    stable_count = 0
    
    while time.time() - start_time < timeout:
        # 检查readyState
        page_state = driver.execute_script('return document.readyState;')
        
        # 获取当前页面源码
        current_page_source = driver.page_source
        
        # 如果页面状态为complete且页面源码连续两次没有变化，认为页面已加载完成
        if page_state == 'complete':
            if current_page_source == last_page_source:
                stable_count += 1
                if stable_count >= 2:  # 连续两次页面内容相同
                    logger.info(f"页面内容已稳定，耗时: {time.time() - start_time:.2f}秒")
                    return True
            else:
                stable_count = 0
                
        last_page_source = current_page_source
        time.sleep(0.5)
        
    raise TimeoutException(f"页面在 {timeout} 秒内未完成加载")

def wait_for_element(driver, by, value, timeout=60):
    """等待元素出现"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except TimeoutException:
        return None

def debug_page(driver, stage="未知阶段"):
    """打印调试信息"""
    logger.info(f"\n===== 调试信息 ({stage}) =====")
    logger.info(f"当前URL: {driver.current_url}")
    logger.info(f"页面标题: {driver.title}")
    logger.info(f"页面状态: {driver.execute_script('return document.readyState;')}")

    # 检查表格是否存在
    try:
        tables = driver.find_elements(By.TAG_NAME, "table")
        logger.info(f"找到 {len(tables)} 个表格")

        if tables:
            logger.info("第一个表格内容预览:")
            rows = tables[0].find_elements(By.TAG_NAME, "tr")
            logger.info(f"表格行数: {len(rows)}")

            # 打印前3行内容
            for i, row in enumerate(rows[:3]):
                try:
                    row_text = row.text
                    logger.info(f"  行 {i+1}: {row_text[:100]}...")
                except:
                    logger.info(f"  行 {i+1}: [无法获取文本]")
    except Exception as e:
        logger.warning(f"获取表格信息出错: {e}")

    logger.info("=====================\n")

def force_refresh_and_retry(driver, url, stage="未知阶段"):
    """强制刷新并重试加载页面"""
    logger.info(f"强制刷新页面并重试 ({stage})...")
    try:
        driver.refresh()
        # 等待页面加载完成
        wait_for_page_load(driver)
        
        # 检查内容
        page_html = driver.execute_script("return document.documentElement.outerHTML;")
        page_length = len(page_html)
        logger.info(f"刷新后页面HTML长度: {page_length} 字符")
        
        # 如果刷新后内容不足，尝试直接访问URL
        if page_length < 1000:
            logger.info("刷新后内容不足，尝试直接访问URL...")
            driver.get(url)
            wait_for_page_load(driver)
            
        return True
    except Exception as e:
        logger.error(f"强制刷新失败: {e}")
        return False

def extract_pairs_from_html(html, market_type, data_type, data_frequency=FREQ_MONTHLY):
    """从HTML源码提取币对信息"""
    # 如果data_type是列表，取第一个元素
    if isinstance(data_type, list) and data_type:
        data_type = data_type[0]
        
    # 根据市场类型和数据类型构建正则表达式
    pattern = fr'href="[^"]*prefix=data/{market_type}/{data_frequency}/{data_type}/([^/"]+)/'
    matches = re.findall(pattern, html)
    # 过滤掉空值和 ".."
    unique_pairs = set(match for match in matches if match and match != "..")
    return unique_pairs

def get_pairs_with_visible_browser(driver, market_type, data_type, data_frequency=FREQ_MONTHLY):
    """使用可见浏览器获取指定市场和数据类型的所有币对"""
    # 构建币对列表页面URL
    pairs_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/"
    logger.info(f"正在打开{market_type}市场{data_frequency}/{data_type}数据的币对列表页面: {pairs_url}")

    # 带重试机制加载页面
    if not load_page_with_retry(driver, pairs_url):
        logger.warning("币对列表页面加载失败，进行强制刷新...")
        force_refresh_and_retry(driver, pairs_url, "币对列表页面")

    # 调试页面
    debug_page(driver, "币对列表页面加载后")

    # 使用JavaScript检查页面内容
    try:
        # 确保几次尝试获取页面内容
        for attempt in range(3):
            page_html = driver.execute_script("return document.documentElement.outerHTML;")
            logger.info(f"页面HTML长度: {len(page_html)} 字符")

            # 使用正则表达式直接在整个HTML中寻找币对
            unique_pairs = extract_pairs_from_html(page_html, market_type, data_type, data_frequency)

            if unique_pairs:
                logger.info(f"从HTML直接找到 {len(unique_pairs)} 个币对")
                pairs = []
                for pair_name in unique_pairs:
                    pair_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair_name}/"
                    pairs.append((pair_name, pair_url))
                return pairs

            logger.warning(f"尝试 {attempt+1}/3: 未找到币对，等待5秒后重试...")
            time.sleep(5)
            driver.refresh()
            time.sleep(10)
    except Exception as e:
        logger.error(f"使用JavaScript获取页面HTML失败: {e}")

    # 尝试查看页面源码
    try:
        logger.info("尝试从页面源码中提取信息...")
        page_source = driver.page_source
        unique_pairs = extract_pairs_from_html(page_source, market_type, data_type, data_frequency)

        if unique_pairs:
            logger.info(f"从页面源码找到 {len(unique_pairs)} 个币对")
            pairs = []
            for pair_name in unique_pairs:
                pair_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair_name}/"
                pairs.append((pair_name, pair_url))
            return pairs
    except Exception as e:
        logger.error(f"从页面源码提取信息失败: {e}")

    # 尝试使用Selenium查找表格中的所有行（不仅仅是第一个表格）
    logger.info("尝试使用Selenium查找表格元素...")
    try:
        tables = driver.find_elements(By.TAG_NAME, "table")
        logger.info(f"找到 {len(tables)} 个表格")

        all_pairs = []

        for table_idx, table in enumerate(tables):
            try:
                rows = table.find_elements(By.TAG_NAME, "tr")
                logger.info(f"表格 {table_idx+1} 有 {len(rows)} 行")

                for row in rows:
                    try:
                        cells = row.find_elements(By.TAG_NAME, "td")
                        if len(cells) >= 1:
                            links = cells[0].find_elements(By.TAG_NAME, "a")
                            for link in links:
                                href = link.get_attribute("href")
                                text = link.text.strip()
                                if text and text != ".." and f"{data_type}" in href:
                                    match = re.search(fr'{data_type}/([^/]+)/', href)
                                    if match:
                                        pair_name = match.group(1)
                                        all_pairs.append((pair_name, href))
                    except Exception as e:
                        logger.warning(f"处理表格行时出错: {e}")
                        continue
            except Exception as e:
                logger.warning(f"处理表格 {table_idx+1} 时出错: {e}")
                continue

        if all_pairs:
            logger.info(f"从表格找到 {len(all_pairs)} 个币对")
            return all_pairs
    except Exception as e:
        logger.error(f"获取表格失败: {e}")

    # 最后的备用方法：直接查找页面上所有链接
    logger.info("尝试查找页面上所有链接...")
    try:
        all_links = driver.find_elements(By.TAG_NAME, "a")
        logger.info(f"页面上共有 {len(all_links)} 个链接")

        pairs = []
        for link in all_links:
            try:
                href = link.get_attribute("href")
                text = link.text.strip()
                if href and f"{data_type}" in href and text and text != "..":
                    match = re.search(fr'{data_type}/([^/]+)/', href)
                    if match:
                        pair_name = match.group(1)
                        pairs.append((pair_name, href))
            except:
                continue

        if pairs:
            logger.info(f"通过检查所有链接找到 {len(pairs)} 个币对")
            return pairs
    except Exception as e:
        logger.error(f"获取所有链接失败: {e}")

    # 最终尝试：手动构建一些常见币对进行测试
    logger.warning("无法获取币对列表，使用常见币对列表...")
    common_pairs = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "DOTUSDT",
                  "XRPUSDT", "DOGEUSDT", "LTCUSDT", "1000SHIBUSDT", "1000000MOGUSDT"]

    pairs = []
    for pair in common_pairs:
        pairs.append((pair, f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair}/"))

    logger.info(f"使用 {len(pairs)} 个常见币对继续...")
    return pairs

def get_data_files_with_visible_browser(driver, pair_name, pair_url, interval, data_frequency=FREQ_MONTHLY, data_type=None):
    """使用可见浏览器获取特定币对的指定时间周期数据文件链接"""
    # 如果data_type是列表，取第一个元素
    if isinstance(data_type, list) and data_type:
        data_type = data_type[0]
        
    # 构建指定时间周期的URL
    interval_url = pair_url
    # 检查是否是K线相关的数据类型
    if isinstance(data_type, list) and data_type:
        # 如果是列表，检查第一个元素
        current_data_type = data_type[0]
        is_kline_data = current_data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
    else:
        is_kline_data = data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
    if is_kline_data and interval:
        interval_url = f"{pair_url}{interval}/"

    # 调试输出完整URL
    logger.info(f"正在访问 {pair_name} 的{data_type}数据页面: {interval_url}")

    # 带重试机制加载页面
    if not load_page_with_retry(driver, interval_url):
        logger.warning(f"无法加载 {pair_name} 的数据页面，尝试强制刷新...")
        force_refresh_and_retry(driver, interval_url, f"{pair_name} 数据页面")

    # 使用JavaScript直接获取页面内容
    try:
        page_html = driver.execute_script("return document.documentElement.outerHTML;")

        # 使用正则表达式寻找zip文件链接
        pattern = r'href="([^"]+\.zip)"'
        matches = re.findall(pattern, page_html)

        files = []
        for file_path in matches:
            # 构建完整URL
            if not file_path.startswith('http'):
                file_url = f"{BASE_URL}{file_path}" if not file_path.startswith('/') else f"{BASE_URL}/{file_path}"
            else:
                file_url = file_path
            files.append(file_url)

        if files:
            logger.info(f"  从HTML直接找到 {len(files)} 个数据文件")
            return files
    except Exception as e:
        logger.warning(f"  使用JavaScript获取页面内容失败: {e}")

    # 尝试从页面源码提取
    try:
        page_source = driver.page_source
        pattern = r'href="([^"]+\.zip)"'
        matches = re.findall(pattern, page_source)

        files = []
        for file_path in matches:
            if not file_path.startswith('http'):
                file_url = f"{BASE_URL}{file_path}" if not file_path.startswith('/') else f"{BASE_URL}/{file_path}"
            else:
                file_url = file_path
            files.append(file_url)

        if files:
            logger.info(f"  从页面源码找到 {len(files)} 个数据文件")
            return files
    except Exception as e:
        logger.warning(f"  从页面源码提取链接失败: {e}")

    # 尝试使用Selenium元素查找所有表格
    try:
        tables = driver.find_elements(By.TAG_NAME, "table")
        logger.info(f"  找到 {len(tables)} 个表格")

        all_files = []

        # 遍历所有表格
        for table_idx, table in enumerate(tables):
            try:
                rows = table.find_elements(By.TAG_NAME, "tr")
                logger.info(f"  表格 {table_idx+1} 有 {len(rows)} 行")

                for row in rows:
                    try:
                        cells = row.find_elements(By.TAG_NAME, "td")
                        if len(cells) >= 1:
                            links = cells[0].find_elements(By.TAG_NAME, "a")
                            for link in links:
                                href = link.get_attribute("href")
                                text = link.text.strip()
                                if text and text.endswith('.zip'):
                                    all_files.append(href)
                    except:
                        continue
            except:
                continue

        if all_files:
            logger.info(f"  从表格找到 {len(all_files)} 个数据文件")
            return all_files
    except Exception as e:
        logger.warning(f"  获取表格失败: {e}")

    # 使用XPath直接查找zip链接
    try:
        zip_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.zip')]")
        files = [link.get_attribute("href") for link in zip_links if link.get_attribute("href")]
        if files:
            logger.info(f"  使用XPath找到 {len(files)} 个数据文件")
            return files
    except Exception as e:
        logger.warning(f"  使用XPath查找zip链接失败: {e}")

    # 最后尝试：查找所有链接
    try:
        all_links = driver.find_elements(By.TAG_NAME, "a")
        files = []
        for link in all_links:
            try:
                href = link.get_attribute("href")
                text = link.text.strip()
                if href and href.endswith('.zip'):
                    files.append(href)
            except:
                continue

        if files:
            logger.info(f"  通过检查所有链接找到 {len(files)} 个数据文件")
            return files
    except Exception as e:
        logger.warning(f"  获取所有链接失败: {e}")

    logger.warning(f"  无法获取 {pair_name} 的数据文件")
    return []

def save_links_to_file(all_links, filename):
    """保存链接到文件"""
    with open(filename, "w") as f:
        for pair_name, links in all_links.items():
            f.write(f"=== {pair_name} ===\n")
            for link in links:
                f.write(f"{link}\n")
            f.write("\n")
    logger.info(f"已保存中间结果到: {filename}")

def load_existing_results(filename, is_interval_file=False):
    """加载现有的结果文件
    
    Args:
        filename: 要加载的文件名
        is_interval_file: 是否是包含时间周期的文件
        
    Returns:
        加载的数据字典
    """
    if not os.path.exists(filename):
        logger.warning(f"结果文件 {filename} 不存在，将创建新文件")
        return {}

    try:
        with open(filename, 'r') as f:
            content = f.read()
            
        if is_interval_file:
            # 检查文件是否包含时间周期信息
            interval_match = re.search(r'===\s*时间周期:\s*([^\n=]+)\s*===', content)
            file_interval = interval_match.group(1).strip() if interval_match else INTERVAL_1d
            
            # 处理包含时间周期的内容
            pairs_data = {}
            
            # 首先分割币对部分
            pair_sections = re.split(r'===\s*([^=\n]+)\s*===', content)
            # 去掉前面的内容(时间和时间周期信息)
            for i in range(len(pair_sections)):
                if '时间周期' in pair_sections[i]:
                    pair_sections = pair_sections[i+1:]
                    break
                    
            # 每两个元素为一组 (币对名称, 链接列表)
            for i in range(0, len(pair_sections), 2):
                if i + 1 < len(pair_sections):
                    pair_name = pair_sections[i].strip()
                    links_text = pair_sections[i + 1].strip()
                    links = [line.strip() for line in links_text.split('\n') if line.strip()]
                    
                    if pair_name and links:
                        pairs_data[pair_name] = {file_interval: links}
            
            logger.info(f"成功加载包含时间周期的结果文件，包含 {len(pairs_data)} 个币对")
            return pairs_data
        else:
            # 使用正则表达式分割币对部分
            pair_sections = re.split(r'===\s*([^=]+)\s*===', content)

            # 去掉第一个空元素
            if pair_sections and not pair_sections[0].strip():
                pair_sections = pair_sections[1:]

            pairs_data = {}

            # 每两个元素为一组 (币对名称, 链接列表)
            for i in range(0, len(pair_sections), 2):
                if i + 1 < len(pair_sections):
                    pair_name = pair_sections[i].strip()
                    links_text = pair_sections[i + 1].strip()
                    
                    # 检查是否包含时间周期格式
                    interval_sections = re.split(r'---\s*([^-]+)\s*---', links_text)
                    
                    if len(interval_sections) > 1:
                        # 处理包含时间周期的格式
                        if not pair_name in pairs_data:
                            pairs_data[pair_name] = {}
                            
                        # 去掉第一个空元素
                        if interval_sections and not interval_sections[0].strip():
                            interval_sections = interval_sections[1:]
                            
                        # 每两个元素为一组 (时间周期, 链接列表)
                        for j in range(0, len(interval_sections), 2):
                            if j + 1 < len(interval_sections):
                                interval = interval_sections[j].strip()
                                interval_links = [line.strip() for line in interval_sections[j + 1].split('\n') if line.strip()]
                                pairs_data[pair_name][interval] = interval_links
                    else:
                        # 处理普通格式
                        links = [line.strip() for line in links_text.split('\n') if line.strip()]
                        pairs_data[pair_name] = links

            logger.info(f"成功加载现有结果文件，包含 {len(pairs_data)} 个币对")
            return pairs_data

    except Exception as e:
        logger.error(f"加载现有结果文件时出错: {e}")
        return {}

# 文件命名规则说明:
# 1. 标准K线数据文件命名格式: {market}_{frequency}_{data_type}_all_links.txt
#    例如: futures_cm_monthly_klines_all_links.txt
# 
# 2. 非标准时间周期K线数据命名格式: {market}_{frequency}_{data_type}_{interval}_all_links.txt
#    例如: futures_cm_monthly_klines_1m_all_links.txt (1分钟K线)
#
# 3. 新增数据文件命名格式: {market}_{data_type}_{interval}_new_data_{date}.txt
#    例如: futures_cm_klines_1d_new_data_20230501.txt 或 futures_cm_klines_1m_new_data_20230501.txt
#
# 注意: 1d (1天)周期作为标准周期，不会在文件名中添加后缀，保持兼容现有系统。
# 其他时间周期(1m, 5m, 15m等)会在文件名中添加对应后缀。

def save_new_data(new_data, market_type=None, data_type=None, interval=None):
    """保存新增数据到独立文件"""
    if not new_data:
        logger.info("没有新增数据，跳过保存")
        return
        
    # 使用全局配置或传入的参数
    market_type = market_type or config["market_type"]
    
    # 如果data_type是列表，则使用第一个元素（这个函数只处理单个数据类型）
    if data_type is None:
        if isinstance(config["data_type"], list):
            data_type = config["data_type"][0]
        else:
            data_type = config["data_type"]
    elif isinstance(data_type, list):
        data_type = data_type[0]

    # 创建保存新增数据的目录
    new_data_dir = "new_data"
    if not os.path.exists(new_data_dir):
        os.makedirs(new_data_dir)

    # 生成带日期和市场类型的文件名
    date_str = datetime.now().strftime("%Y%m%d")
    market_str = market_type.replace("/", "_")
    
    # 检查是否需要添加时间周期后缀
    interval_suffix = ""
    if interval and interval != INTERVAL_1d:
        interval_suffix = f"_{interval}"
        
    new_data_file = os.path.join(new_data_dir, f"{market_str}_{data_type}{interval_suffix}_new_data_{date_str}.txt")

    with open(new_data_file, "w") as f:
        f.write(f"=== 采集日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
        f.write(f"=== 市场类型: {market_type} ===\n")
        f.write(f"=== 数据类型: {data_type} ===\n")
        if interval:
            f.write(f"=== 时间周期: {interval} ===\n")
        f.write("\n")
        
        for pair_name, links in new_data.items():
            f.write(f"=== {pair_name} ===\n")
            for link in links:
                f.write(f"{link}\n")
            f.write("\n")

    logger.info(f"已保存新增数据到: {new_data_file}")
    return new_data_file

def save_new_data_with_intervals(new_data, market_type=None, data_type=None):
    """保存带时间周期的新增数据到独立文件"""
    if not new_data:
        logger.info("没有新增数据，跳过保存")
        return
        
    # 使用全局配置或传入的参数
    market_type = market_type or config["market_type"]
    
    # 如果data_type是列表，则使用第一个元素（这个函数只处理单个数据类型）
    if data_type is None:
        if isinstance(config["data_type"], list):
            data_type = config["data_type"][0]
        else:
            data_type = config["data_type"]
    elif isinstance(data_type, list):
        data_type = data_type[0]

    # 创建保存新增数据的目录
    new_data_dir = "new_data"
    if not os.path.exists(new_data_dir):
        os.makedirs(new_data_dir)

    # 按时间周期分别保存文件
    saved_files = []
    
    # 获取所有唯一的时间周期
    all_intervals = set()
    for pair_data in new_data.values():
        all_intervals.update(pair_data.keys())
    
    # 对每个时间周期分别保存文件
    for interval in all_intervals:
        # 提取当前时间周期的数据
        interval_data = {}
        for pair_name, intervals_data in new_data.items():
            if interval in intervals_data:
                if not intervals_data[interval]:  # 跳过空数据
                    continue
                    
                if pair_name not in interval_data:
                    interval_data[pair_name] = []
                interval_data[pair_name] = intervals_data[interval]
        
        # 如果该时间周期没有数据，跳过
        if not interval_data:
            continue
            
        # 保存该时间周期的数据
        saved_file = save_new_data(interval_data, market_type, data_type, interval)
        saved_files.append(saved_file)
    
    return saved_files

def save_links_to_file_with_intervals(all_links, filename_base):
    """保存带时间周期的链接到文件
    
    Args:
        all_links: 包含不同时间周期的链接数据，格式为 {pair_name: {interval: [links]}}
        filename_base: 基础文件名，不包含扩展名
    
    Returns:
        保存的文件列表
    """
    # 确定所有唯一的时间周期
    all_intervals = set()
    for pair_data in all_links.values():
        all_intervals.update(pair_data.keys())
    
    saved_files = []
    
    # 对每个时间周期分别保存文件
    for interval in all_intervals:
        # 根据时间周期生成文件名
        if interval == INTERVAL_1d:
            # 1d周期使用原始文件名
            filename = f"{filename_base}_all_links.txt"
        else:
            # 其他周期添加后缀
            filename = f"{filename_base}_{interval}_all_links.txt"
        
        # 提取该时间周期的数据
        with open(filename, "w") as f:
            f.write(f"=== 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            f.write(f"=== 时间周期: {interval} ===\n\n")
            
            for pair_name, intervals_data in all_links.items():
                if interval in intervals_data and intervals_data[interval]:
                    f.write(f"=== {pair_name} ===\n")
                    links = intervals_data[interval]
                    for link in links:
                        f.write(f"{link}\n")
                    f.write("\n")
        
        logger.info(f"已保存{interval}时间周期的链接到: {filename}")
        saved_files.append(filename)
    
    return saved_files

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="币安数据爬虫 - 获取币安各种市场和数据类型的链接")

    # 市场类型参数
    parser.add_argument("--market", "-m", type=str, choices=[MARKET_SPOT, MARKET_FUTURES_UM, MARKET_FUTURES_CM],
                        default=DEFAULT_CONFIG["market_type"],
                        help="市场类型: spot(现货), futures/um(U本位合约), futures/cm(币本位合约)")

    # 数据类型参数
    parser.add_argument("--data", "-d", type=str, nargs="+",
                        choices=[DATA_KLINES, DATA_TRADES, DATA_AGGRADES, DATA_DEPTH, 
                                DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX,
                                DATA_BOOKTICKER, DATA_FUNDING, DATA_METRICS, DATA_LIQUIDATION],
                        default=[DEFAULT_CONFIG["data_type"]],
                        help="数据类型: klines(K线), trades(交易), aggTrades(聚合交易), depth(深度)等")

    # 是否启用多数据类型爬取
    parser.add_argument("--multi", action="store_true",
                        default=DEFAULT_CONFIG["multi_data_types"],
                        help="是否启用多数据类型爬取")

    # 时间周期参数（仅对K线数据有效）
    parser.add_argument("--intervals", "-i", type=str, nargs="+",
                        choices=[INTERVAL_1m, INTERVAL_5m, INTERVAL_15m, INTERVAL_30m,
                                INTERVAL_1h, INTERVAL_2h, INTERVAL_4h, INTERVAL_6h, INTERVAL_8h, INTERVAL_12h,
                                INTERVAL_1d, INTERVAL_3d, INTERVAL_1w, INTERVAL_1mo],
                        default=DEFAULT_CONFIG["intervals"],
                        help="时间周期（仅对K线数据有效）")

    # 数据频率参数
    parser.add_argument("--frequency", "-f", type=str, choices=[FREQ_DAILY, FREQ_MONTHLY],
                        default=DEFAULT_CONFIG["data_frequency"],
                        help="数据频率: daily(每日数据), monthly(月度数据)")

    # 输出目录参数
    parser.add_argument("--output", "-o", type=str, default=DEFAULT_CONFIG["output_dir"],
                        help="输出目录路径")

    # Chrome浏览器路径参数
    parser.add_argument("--chrome", "-c", type=str, default=DEFAULT_CONFIG["chrome_path"],
                        help="Chrome浏览器可执行文件路径")

    args = parser.parse_args()

    # 更新全局配置
    config["market_type"] = args.market
    config["multi_data_types"] = args.multi
    
    # 根据是否启用多数据类型爬取来决定要爬取的数据类型
    if config["multi_data_types"]:
        config["data_type"] = DEFAULT_CONFIG["data_types_list"]
    else:
        config["data_type"] = [args.data[0] if isinstance(args.data, list) else args.data]
        
    config["intervals"] = args.intervals
    config["output_dir"] = args.output
    config["chrome_path"] = args.chrome
    config["data_frequency"] = args.frequency

    return config

def process_missing_pairs_with_intervals(missing_pairs, driver, all_links, market_type, data_type, data_frequency=FREQ_MONTHLY):
    """处理带有时间周期的缺失币对

    Args:
        missing_pairs: 缺失的币对和时间周期列表，格式为[(pair_name, interval), ...]
        driver: WebDriver实例
        all_links: 所有链接的字典
        market_type: 市场类型
        data_type: 数据类型
        data_frequency: 数据频率

    Returns:
        补充获取的数据字典，格式为{pair_name: {interval: [links]}}
    """
    logger.info(f"开始处理 {len(missing_pairs)} 个缺失的币对和时间周期...")

    supplementary_data = {}

    for i, (pair_name, interval) in enumerate(missing_pairs):
        logger.info(f"补充爬取 {i+1}/{len(missing_pairs)}: {pair_name} {interval}")

        # 构建URL
        pair_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair_name}/"
        
        # 根据数据类型决定是否传递时间周期参数
        if data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]:
            data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, interval, data_frequency, data_type)
        else:
            # 非K线数据不使用时间周期
            data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, "", data_frequency, data_type)

        if data_files:
            # 更新all_links
            if pair_name not in all_links:
                all_links[pair_name] = {}
            all_links[pair_name][interval] = data_files

            # 添加到补充数据
            if pair_name not in supplementary_data:
                supplementary_data[pair_name] = {}
            supplementary_data[pair_name][interval] = data_files

            logger.info(f"成功补充获取 {pair_name} {interval} 的 {len(data_files)} 个数据文件")
        else:
            logger.warning(f"无法获取 {pair_name} {interval} 的数据文件")

    return supplementary_data

def save_daily_summary(all_new_data, market_type, data_types, date_str=None):
    """保存每日汇总数据，将所有新增链接汇总到一个文件中
    
    Args:
        all_new_data: 所有新增数据的字典，格式为 {data_type: {pair_name: {interval: [links]}}} 或 {data_type: {pair_name: [links]}}
        market_type: 市场类型
        data_types: 数据类型列表
        date_str: 日期字符串，默认为当前日期
        
    Returns:
        汇总文件路径
    """
    if not all_new_data:
        logger.info("没有新增数据，跳过保存汇总")
        return None
        
    # 创建保存新增数据的目录
    new_data_dir = "new_data"
    if not os.path.exists(new_data_dir):
        os.makedirs(new_data_dir)
        
    # 生成带日期的文件名
    if date_str is None:
        date_str = datetime.now().strftime("%Y%m%d")
        
    market_str = market_type.replace("/", "_")
    summary_file = os.path.join(new_data_dir, f"{market_str}_summary_{date_str}.txt")
    
    # 统计链接数量
    total_pairs = 0
    total_links = 0
    
    with open(summary_file, "w") as f:
        f.write(f"=== 币安{market_type}市场新增数据汇总 ===\n")
        f.write(f"=== 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n\n")
        
        # 对每种数据类型分别处理
        for data_type in data_types:
            if data_type not in all_new_data or not all_new_data[data_type]:
                f.write(f"=== {data_type} 数据类型: 无新增数据 ===\n\n")
                continue
                
            f.write(f"=== {data_type} 数据类型 ===\n")
            
            # 统计当前数据类型的链接数量
            pair_count = 0
            link_count = 0
            
            # 检查是否是K线相关数据类型
            is_kline_data = data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
            
            for pair_name, pair_data in all_new_data[data_type].items():
                pair_count += 1
                
                if is_kline_data and isinstance(pair_data, dict):
                    # K线数据，按时间周期分组展示
                    f.write(f"\n>> {pair_name} 币对 <<\n")
                    
                    for interval, links in pair_data.items():
                        if not links:
                            continue
                            
                        f.write(f"  - {interval} 周期 ({len(links)} 个文件):\n")
                        for link in links:
                            f.write(f"    {link}\n")
                            link_count += 1
                            
                else:
                    # 非K线数据或旧格式数据
                    links = pair_data
                    if not links:
                        continue
                        
                    f.write(f"\n>> {pair_name} 币对 ({len(links)} 个文件) <<\n")
                    for link in links:
                        f.write(f"  {link}\n")
                        link_count += 1
                
            total_links += link_count
            total_pairs += pair_count
            f.write(f"\n>> {data_type} 总计: {pair_count} 个交易对, {link_count} 个链接 <<\n\n")
        
        f.write(f"\n=== 总计: {total_pairs} 个交易对, {total_links} 个链接 ===\n")
        
    logger.info(f"已保存当日汇总数据到: {summary_file}")
    return summary_file

# 使用示例:
# 1. 直接运行，使用默认配置: python binance_crawler.py
# 2. 启用多数据类型爬取: python binance_crawler.py --multi
# 3. 手动指定数据类型: python binance_crawler.py --data klines trades
# 4. 修改DEFAULT_CONFIG中的配置:
#    - multi_data_types: True 启用多数据类型爬取
#    - data_types_list: 自定义要爬取的数据类型列表 如 [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE]

def main():
    """主函数"""
    try:
        # 解析命令行参数
        config = parse_arguments()
        market_type = config["market_type"]
        data_types = config["data_type"]  # 现在是一个列表
        intervals = config["intervals"]
        output_dir = config["output_dir"]
        chrome_path = config["chrome_path"]
        data_frequency = config["data_frequency"]

        # 记录当前日期，用于汇总文件
        today_date = datetime.now().strftime("%Y%m%d")
        
        logger.info(f"开始爬取币安{market_type}市场的{data_frequency}数据，类型：{', '.join(data_types)}")

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 设置可见的Chrome浏览器
        driver = setup_visible_browser()
        
        # 用于保存所有新增数据的字典，按数据类型组织
        all_new_data = {}
        
        # 对每种数据类型进行爬取
        for data_type in data_types:
            logger.info(f"\n--- 开始爬取 {data_type} 类型数据 ---")
            
            # 获取所有交易对
            all_pairs = get_pairs_with_visible_browser(driver, market_type, data_type, data_frequency)
            logger.info(f"总共找到 {len(all_pairs)} 个交易对")

            # 根据市场类型、数据类型和数据频率生成文件名
            file_prefix = f"{market_type.replace('/', '_')}_{data_frequency}_{data_type}"
            all_links_file_base = os.path.join(output_dir, file_prefix)

            # 加载现有结果（需要考虑不同的时间周期文件）
            existing_results = {}
            
            # 检查是否是K线相关数据类型
            if isinstance(data_type, list) and data_type:
                # 如果是列表，检查第一个元素
                current_data_type = data_type[0]
                is_kline_data = current_data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
            else:
                is_kline_data = data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
            
            if is_kline_data:
                # 对于K线数据，需要分别加载每个时间周期的文件
                for interval in intervals:
                    if interval == INTERVAL_1d:
                        # 1d使用原始文件名
                        interval_file = f"{all_links_file_base}_all_links.txt"
                    else:
                        # 其他周期使用带后缀的文件名
                        interval_file = f"{all_links_file_base}_{interval}_all_links.txt"
                        
                    # 尝试加载该时间周期的文件
                    if os.path.exists(interval_file):
                        interval_results = load_existing_results(interval_file, True)
                        
                        # 合并结果
                        for pair_name, pair_data in interval_results.items():
                            if pair_name not in existing_results:
                                existing_results[pair_name] = {}
                            # 合并该币对的不同时间周期
                            for int_key, links in pair_data.items():
                                existing_results[pair_name][int_key] = links
                    else:
                        # 尝试加载旧格式的文件（历史兼容）
                        old_file = f"{all_links_file_base}_all_links.txt"
                        if os.path.exists(old_file) and not existing_results:
                            old_results = load_existing_results(old_file)
                            
                            # 如果旧格式文件包含时间周期信息
                            for pair_name, pair_data in old_results.items():
                                if isinstance(pair_data, dict):
                                    # 已包含时间周期信息
                                    if pair_name not in existing_results:
                                        existing_results[pair_name] = {}
                                    for int_key, links in pair_data.items():
                                        existing_results[pair_name][int_key] = links
            else:
                # 非K线数据，直接加载
                standard_file = f"{all_links_file_base}_all_links.txt"
                if os.path.exists(standard_file):
                    existing_results = load_existing_results(standard_file)
                    
            logger.info(f"已加载现有结果，包含 {len(existing_results)} 个币对")

            # 存储所有链接和新增链接
            all_links = {}
            new_data = {}

            # 检查数据类型是否是K线相关的类型
            if isinstance(data_type, list) and data_type:
                # 如果是列表，检查第一个元素
                current_data_type = data_type[0]
                is_kline_data = current_data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
            else:
                is_kline_data = data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]
            
            # 使用tqdm创建进度条
            pair_progress = tqdm(all_pairs, desc=f"爬取{data_type}交易对", ncols=100)
            
            # 如果是K线相关数据，需要遍历所有时间周期
            if is_kline_data:
                # 针对每个交易对获取数据文件
                for i, (pair_name, pair_url) in enumerate(pair_progress):
                    pair_progress.set_description(f"处理 {pair_name}")
                    
                    # 初始化该币对的链接字典
                    all_links[pair_name] = {}
                    
                    # 对每个时间周期使用内部进度条
                    if len(intervals) > 1:
                        interval_progress = tqdm(intervals, desc=f"{pair_name}周期", leave=False, ncols=80)
                    else:
                        interval_progress = intervals
                        
                    # 遍历所有时间周期
                    for interval in interval_progress:
                        if len(intervals) > 1:
                            interval_progress.set_description(f"{pair_name} {interval}")
                            
                        # 获取该交易对的指定时间周期数据文件
                        data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, interval, data_frequency, data_type)

                        # 存储该交易对的指定时间周期的链接
                        all_links[pair_name][interval] = data_files

                        # 检查是否有新增数据
                        if pair_name in existing_results and interval in existing_results[pair_name]:
                            # 找出新增的链接
                            new_links = [link for link in data_files if link not in existing_results[pair_name][interval]]
                            if new_links:
                                tqdm.write(f"发现 {pair_name} 交易对 {interval} 周期有 {len(new_links)} 个新增数据")
                                if pair_name not in new_data:
                                    new_data[pair_name] = {}
                                new_data[pair_name][interval] = new_links
                        else:
                            # 新增的交易对或时间周期
                            if data_files:
                                tqdm.write(f"发现新增数据: {pair_name} {interval}")
                                if pair_name not in new_data:
                                    new_data[pair_name] = {}
                                new_data[pair_name][interval] = data_files

                    # 每处理10个交易对就先保存一次结果
                    if (i + 1) % 10 == 0:
                        save_links_to_file_with_intervals(all_links, all_links_file_base)
            else:
                # 对于非K线数据，直接获取数据文件（不使用时间周期）
                for i, (pair_name, pair_url) in enumerate(pair_progress):
                    pair_progress.set_description(f"处理 {pair_name}")
                    
                    # 获取该交易对的所有数据文件，传入空字符串作为时间周期
                    data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, "", data_frequency, data_type)

                    # 存储该交易对的所有链接
                    all_links[pair_name] = data_files

                    # 检查是否有新增数据
                    if pair_name in existing_results:
                        # 找出新增的链接
                        new_links = [link for link in data_files if link not in existing_results[pair_name]]
                        if new_links:
                            tqdm.write(f"发现 {pair_name} 交易对有 {len(new_links)} 个新增数据")
                            new_data[pair_name] = new_links
                    else:
                        # 新增的交易对
                        if data_files:
                            tqdm.write(f"发现新增交易对: {pair_name}")
                            new_data[pair_name] = data_files

                    # 每处理10个交易对就先保存一次结果
                    if (i + 1) % 10 == 0:
                        save_links_to_file(all_links, f"{all_links_file_base}_all_links.txt")

            # 根据数据类型保存新增数据
            if is_kline_data:
                saved_files = save_links_to_file_with_intervals(all_links, all_links_file_base)
                saved_new_files = save_new_data_with_intervals(new_data, market_type, data_type)
            else:
                save_links_to_file(all_links, f"{all_links_file_base}_all_links.txt")
                save_new_data(new_data, market_type, data_type)
                
            # 将当前数据类型的新增数据添加到汇总
            if new_data:
                all_new_data[data_type] = new_data
                
            # 运行补充爬虫检查缺失数据
            run_supplementary_crawler = True
            if run_supplementary_crawler:
                logger.info(f"检查 {data_type} 是否有缺失数据...")

                # 检查是否有空数据的交易对
                missing_pairs = []
                for pair_name, links in all_links.items():
                    if not links:
                        missing_pairs.append(pair_name)

                # 导入supplementary_crawler模块的process_missing_pairs函数
                if missing_pairs:
                    try:
                        from supplementary_crawler import process_missing_pairs

                        logger.info(f"发现 {len(missing_pairs)} 个交易对缺失数据，准备补充爬取...")

                        # 根据数据类型决定是否传递时间周期参数
                        if is_kline_data:
                            # 对于K线数据，需要传递时间周期
                            # 使用默认的1d周期或第一个配置的周期
                            interval_to_use = intervals[0] if intervals else INTERVAL_1d
                            supplementary_data = process_missing_pairs(
                                missing_pairs, 
                                driver, 
                                all_links, 
                                market_type=market_type, 
                                data_type=data_type, 
                                interval=interval_to_use, 
                                data_frequency=data_frequency
                            )
                        else:
                            # 对于非K线数据，不需要传递时间周期参数
                            supplementary_data = process_missing_pairs(
                                missing_pairs, 
                                driver, 
                                all_links, 
                                market_type=market_type, 
                                data_type=data_type, 
                                data_frequency=data_frequency
                            )

                        # 更新新增数据
                        for pair_name, links in supplementary_data.items():
                            if pair_name in new_data:
                                # 添加不重复的链接
                                new_data[pair_name].extend([link for link in links if link not in new_data[pair_name]])
                            else:
                                new_data[pair_name] = links

                        # 再次保存所有链接
                        if is_kline_data:
                            save_links_to_file_with_intervals(all_links, all_links_file_base)
                        else:
                            save_links_to_file(all_links, f"{all_links_file_base}_all_links.txt")

                        # 更新新增数据文件
                        if supplementary_data:
                            if is_kline_data:
                                save_new_data_with_intervals(new_data, market_type, data_type)
                            else:
                                save_new_data(new_data, market_type, data_type)
                            
                            # 更新汇总数据
                            if data_type in all_new_data:
                                for pair_name, links in supplementary_data.items():
                                    if pair_name in all_new_data[data_type]:
                                        all_new_data[data_type][pair_name].extend(
                                            [link for link in links if link not in all_new_data[data_type][pair_name]]
                                        )
                                    else:
                                        all_new_data[data_type][pair_name] = links
                    except ImportError:
                        logger.warning("无法导入supplementary_crawler模块，将使用内置方法处理缺失数据")

                        # 使用内置方法处理缺失币对
                        if missing_pairs:
                            logger.info(f"发现 {len(missing_pairs)} 个交易对缺失数据，准备补充爬取...")

                            # 针对每个缺失的交易对尝试再次爬取
                            for i, pair_name in enumerate(tqdm(missing_pairs, desc="补充爬取")):
                                # 构建URL
                                pair_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair_name}/"
                                
                                # 根据数据类型决定是否传递时间周期参数
                                if data_type in [DATA_KLINES, DATA_INDEXPRICE, DATA_MARKPRICE, DATA_PREMIUMINDEX]:
                                    data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, INTERVAL_1d, data_frequency, data_type)
                                else:
                                    # 非K线数据不使用时间周期
                                    data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, "", data_frequency, data_type)

                                if data_files:
                                    all_links[pair_name] = data_files
                                    # 添加到新增数据
                                    new_data[pair_name] = data_files
                                    tqdm.write(f"成功补充获取 {pair_name} 的 {len(data_files)} 个数据文件")
                                    
                                    # 更新汇总数据
                                    if data_type in all_new_data:
                                        all_new_data[data_type][pair_name] = data_files
                                    else:
                                        all_new_data[data_type] = {pair_name: data_files}
                                else:
                                    tqdm.write(f"仍然无法获取 {pair_name} 的数据文件")

                            # 再次保存更新后的结果
                            save_links_to_file(all_links, f"{all_links_file_base}_all_links.txt")
                            # 更新新增数据文件
                            save_new_data(new_data, market_type, data_type)
                else:
                    logger.info(f"所有 {data_type} 交易对均已成功获取数据，无需补充爬取")
        
        # 保存汇总数据
        if all_new_data:
            summary_file = save_daily_summary(all_new_data, market_type, data_types)
            logger.info(f"所有新增数据已汇总到: {summary_file}")
        else:
            logger.info("没有新增数据，跳过保存汇总")
            
        logger.info("爬取完成！")
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        logger.error(traceback.format_exc())
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"程序运行时发生错误: {e}")
        logger.error(traceback.format_exc())