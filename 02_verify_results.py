import os
import re
import json
from collections import Counter

# 保存链接的目录
output_dir = "binance_links"
results_file = os.path.join(output_dir, "futures_um_monthly_aggTrades_all_links.txt)

def load_results(file_path):
    """加载抓取结果文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在!")
        return None
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        return content
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def parse_results(content):
    """解析结果文件，返回每个币对的数据文件数量"""
    if not content:
        return {}
    
    # 使用正则表达式分割币对部分
    pair_sections = re.split(r'===\s*([^=]+)\s*===', content)
    
    # 去掉第一个空元素
    if pair_sections and not pair_sections[0].strip():
        pair_sections = pair_sections[1:]
    
    pairs_data = {}
    
    # 每两个元素为一组 (币对名称, 链接列表)
    for i in range(0, len(pair_sections), 2):
        if i + 1 < len(pair_sections):
            pair_name = pair_sections[i].strip()
            links_text = pair_sections[i + 1].strip()
            links = [line.strip() for line in links_text.split('\n') if line.strip()]
            pairs_data[pair_name] = links
    
    return pairs_data

def verify_results(pairs_data):
    """验证结果，检查每个币对是否有数据"""
    if not pairs_data:
        print("没有找到任何币对数据!")
        return
    
    total_pairs = len(pairs_data)
    pairs_with_data = sum(1 for links in pairs_data.values() if links)
    pairs_without_data = total_pairs - pairs_with_data
    
    total_files = sum(len(links) for links in pairs_data.values())
    
    # 按文件数量统计币对
    file_count_stats = Counter()
    for pair, links in pairs_data.items():
        file_count_stats[len(links)] += 1
    
    # 显示有最多文件和最少文件的币对
    most_files = 0
    most_files_pair = ""
    least_files = float('inf')
    least_files_pair = ""
    
    for pair, links in pairs_data.items():
        if links:  # 只考虑有文件的币对
            if len(links) > most_files:
                most_files = len(links)
                most_files_pair = pair
                
            if len(links) < least_files:
                least_files = len(links)
                least_files_pair = pair
    
    # 显示结果
    print("\n===== 币对数据抓取验证报告 =====")
    print(f"总币对数: {total_pairs}")
    print(f"成功抓取数据币对数: {pairs_with_data} ({pairs_with_data/total_pairs:.1%})")
    print(f"未抓取到数据币对数: {pairs_without_data} ({pairs_without_data/total_pairs:.1%})")
    print(f"总共抓取文件数: {total_files}")
    print(f"平均每个币对文件数: {total_files/pairs_with_data:.1f}")
    
    print("\n数据文件数量分布:")
    for count in sorted(file_count_stats.keys()):
        print(f"  {count} 个文件: {file_count_stats[count]} 个币对")
    
    if most_files_pair:
        print(f"\n文件最多的币对: {most_files_pair} ({most_files} 个文件)")
    if least_files_pair:
        print(f"文件最少的币对: {least_files_pair} ({least_files} 个文件)")
    
    # 显示没有数据的币对
    if pairs_without_data > 0:
        print("\n以下币对没有抓取到数据:")
        for pair, links in pairs_data.items():
            if not links:
                print(f"  - {pair}")
    
    # 保存详细报告到JSON文件
    report = {
        "total_pairs": total_pairs,
        "pairs_with_data": pairs_with_data,
        "pairs_without_data": pairs_without_data,
        "total_files": total_files,
        "pair_stats": {pair: len(links) for pair, links in pairs_data.items()},
        "file_count_distribution": {str(count): file_count_stats[count] for count in file_count_stats}
    }
    
    with open(os.path.join(output_dir, "verification_report.json"), "w") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细报告已保存到: {os.path.join(output_dir, 'verification_report.json')}")

def main():
    # 检查输出目录是否存在
    if not os.path.exists(output_dir):
        print(f"输出目录 {output_dir} 不存在!")
        return
    
    # 加载结果文件
    print(f"正在加载结果文件: {results_file}")
    content = load_results(results_file)
    
    if not content:
        print("未能成功加载结果文件!")
        return
    
    # 解析结果
    print("正在解析结果...")
    pairs_data = parse_results(content)
    
    # 验证结果
    verify_results(pairs_data)

if __name__ == "__main__":
    main() 