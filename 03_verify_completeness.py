import os
import re
import json
import time
import argparse
from datetime import datetime
from collections import Counter
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

# 从binance_crawler.py导入必要的函数和常量
from binance_crawler import (
    BASE_URL, setup_visible_browser, load_page_with_retry,
    extract_pairs_from_html, wait_for_page_load
)

# 保存链接的目录
output_dir = "binance_links"

# 默认配置
DEFAULT_CONFIG = {
    "mode": "all",  # 默认为批量模式"all"，单文件模式为"single"
    "file": "spot_monthly_trades_all_links.txt",  # 默认文件
    "market_type": "futures/um",  # 默认市场类型
    "data_type": "aggTrades",  # 默认数据类型
    "data_frequency": "monthly",  # 默认数据频率
    "batch_mode": True  # 默认为批量模式，只生成一个汇总报告
}

# 全局配置变量
config = DEFAULT_CONFIG.copy()


def get_all_pairs_from_website(market_type, data_type, data_frequency):
    """从币安数据网站获取指定市场和数据类型的所有可用币对"""
    print(f"正在从币安数据网站获取 {market_type}/{data_frequency}/{data_type} 的所有可用币对...")

    # 设置浏览器
    driver = setup_visible_browser()

    try:
        # 构建币对列表页面URL
        pairs_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/"
        print(f"正在访问: {pairs_url}")

        # 加载页面
        if not load_page_with_retry(driver, pairs_url):
            print("无法加载币对列表页面")
            return set()

        # 等待页面加载完成
        wait_for_page_load(driver)

        # 获取页面HTML
        page_html = driver.page_source

        # 提取币对
        all_pairs = extract_pairs_from_html(page_html, market_type, data_type, data_frequency)

        print(f"从网站获取到 {len(all_pairs)} 个币对")
        return all_pairs

    except Exception as e:
        print(f"获取币对时出错: {e}")
        return set()
    finally:
        # 关闭浏览器
        driver.quit()


def load_results(file_path):
    """加载抓取结果文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在!")
        return None

    try:
        with open(file_path, 'r') as f:
            content = f.read()

        return content
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None


def parse_results(content):
    """解析结果文件，返回每个币对的数据文件数量"""
    if not content:
        return {}

    # 使用正则表达式分割币对部分
    pair_sections = re.split(r'===\s*([^=]+)\s*===', content)

    # 去掉第一个空元素
    if pair_sections and not pair_sections[0].strip():
        pair_sections = pair_sections[1:]

    pairs_data = {}

    # 每两个元素为一组 (币对名称, 链接列表)
    for i in range(0, len(pair_sections), 2):
        if i + 1 < len(pair_sections):
            pair_name = pair_sections[i].strip()
            links_text = pair_sections[i + 1].strip()
            links = [line.strip() for line in links_text.split('\n') if line.strip()]
            pairs_data[pair_name] = links

    return pairs_data


def verify_results_with_website(pairs_data, file_name, market_type, data_type, data_frequency, batch_mode=False):
    """与网站数据比较验证结果"""
    if not pairs_data:
        print("没有找到任何币对数据!")
        return

    # 从结果文件中获取币对集合
    crawled_pairs = set(pairs_data.keys())
    print(f"结果文件中有 {len(crawled_pairs)} 个币对")

    # 从网站获取所有可用币对
    website_pairs = get_all_pairs_from_website(market_type, data_type, data_frequency)

    if not website_pairs:
        print("无法从网站获取币对信息，只进行本地验证")
        verify_results(pairs_data, file_name, batch_mode)
        return

    # 比较两个集合
    missing_pairs = website_pairs - crawled_pairs  # 网站上有但结果中没有的
    extra_pairs = crawled_pairs - website_pairs  # 结果中有但网站上没有的
    common_pairs = crawled_pairs.intersection(website_pairs)  # 两者都有的

    # 显示结果
    print(f"\n===== 币对完整性验证报告 ({file_name}) =====")
    print(f"网站上总币对数: {len(website_pairs)}")
    print(f"爬取结果中总币对数: {len(crawled_pairs)}")
    print(f"成功爬取的币对数: {len(common_pairs)} ({len(common_pairs) / len(website_pairs):.1%})")

    if missing_pairs:
        print(f"\n漏爬的币对数: {len(missing_pairs)} ({len(missing_pairs) / len(website_pairs):.1%})")
        print("漏爬的币对:")
        for pair in sorted(missing_pairs):
            print(f"  - {pair}")

    if extra_pairs:
        print(f"\n多余的币对数: {len(extra_pairs)} ({len(extra_pairs) / len(crawled_pairs):.1%})")
        print("多余的币对:")
        for pair in sorted(extra_pairs):
            print(f"  - {pair}")

    # 继续进行常规验证
    verification_report = verify_results({pair: links for pair, links in pairs_data.items() if pair in common_pairs}, file_name, batch_mode)

    # 找出存在但没有数据的币对（链接为0的币对）
    zero_data_pairs = []
    for pair, links in pairs_data.items():
        if pair in common_pairs and not links:
            zero_data_pairs.append(pair)

    # 准备完整性报告
    completeness_report = {
        "file_name": file_name,
        "market_type": market_type,
        "data_type": data_type,
        "data_frequency": data_frequency,
        "website_pairs_count": len(website_pairs),
        "crawled_pairs_count": len(crawled_pairs),
        "common_pairs_count": len(common_pairs),
        "missing_pairs_count": len(missing_pairs),
        "extra_pairs_count": len(extra_pairs),
        "missing_pairs": list(missing_pairs),
        "zero_data_pairs": zero_data_pairs,  # 添加存在但没有数据的币对
        "completeness_rate": len(common_pairs) / len(website_pairs) if website_pairs else 0
    }

    # 不再为单个文件生成JSON报告
    print(f"\n完整性验证结果已添加到汇总报告中")

    # 合并完整性报告和验证报告
    combined_report = {**completeness_report, **verification_report}
    return combined_report


def verify_results(pairs_data, file_name, batch_mode=False):
    """验证结果，检查每个币对是否有数据"""
    if not pairs_data:
        print("没有找到任何币对数据!")
        return {}

    total_pairs = len(pairs_data)
    pairs_with_data = sum(1 for links in pairs_data.values() if links)
    pairs_without_data = total_pairs - pairs_with_data

    total_files = sum(len(links) for links in pairs_data.values())

    # 按文件数量统计币对
    file_count_stats = Counter()
    for pair, links in pairs_data.items():
        file_count_stats[len(links)] += 1

    # 显示有最多文件和最少文件的币对
    most_files = 0
    most_files_pair = ""
    least_files = float('inf')
    least_files_pair = ""

    # 记录没有数据的币对（文件数为0）
    no_data_pairs = []

    for pair, links in pairs_data.items():
        if links:  # 只考虑有文件的币对
            if len(links) > most_files:
                most_files = len(links)
                most_files_pair = pair

            if len(links) < least_files:
                least_files = len(links)
                least_files_pair = pair
        else:  # 没有数据的币对
            no_data_pairs.append(pair)

    # 显示结果
    print(f"\n===== 币对数据抽取验证报告 ({file_name}) =====")
    print(f"总币对数: {total_pairs}")
    print(f"成功抽取数据币对数: {pairs_with_data} ({pairs_with_data/total_pairs:.1%})")
    print(f"未抽取到数据币对数: {pairs_without_data} ({pairs_without_data/total_pairs:.1%})")
    print(f"总共抽取文件数: {total_files}")
    print(f"平均每个币对文件数: {total_files/pairs_with_data:.1f}" if pairs_with_data else "没有成功抽取的币对")

    print("\n数据文件数量分布:")
    for count in sorted(file_count_stats.keys()):
        print(f"  {count} 个文件: {file_count_stats[count]} 个币对")

    if most_files_pair:
        print(f"\n文件最多的币对: {most_files_pair} ({most_files} 个文件)")
    if least_files_pair:
        print(f"文件最少的币对: {least_files_pair} ({least_files} 个文件)")

    # 显示没有数据的币对
    if pairs_without_data > 0:
        print("\n以下币对没有抽取到数据:")
        for pair in no_data_pairs:
                print(f"  - {pair}")

    # 准备验证报告
    report = {
        "file_name": file_name,
        "total_pairs": total_pairs,
        "pairs_with_data": pairs_with_data,
        "pairs_without_data": pairs_without_data,
        "no_data_pairs": no_data_pairs,  # 添加没有数据的币对列表
        "total_files": total_files,
        "average_files_per_pair": total_files/pairs_with_data if pairs_with_data else 0,
        "file_count_distribution": {str(count): file_count_stats[count] for count in file_count_stats},
        "most_files_pair": most_files_pair,
        "most_files_count": most_files,
        "least_files_pair": least_files_pair,
        "least_files_count": least_files if least_files != float('inf') else 0
    }

    # 不再为单个文件生成JSON报告
    print(f"\n验证结果已添加到汇总报告中")

    return report


def parse_file_info(file_name):
    """从文件名解析市场类型、数据频率和数据类型"""
    # 默认值
    market_type = "futures/um"
    data_frequency = "monthly"
    data_type = "aggTrades"

    # 尝试从文件名解析
    # 先删除后缀
    base_name = file_name.replace("_all_links.txt", "")

    # 分割市场类型和其他部分
    if "spot_" in base_name:
        market_type = "spot"
        rest = base_name.replace("spot_", "")
    elif "futures_um_" in base_name:
        market_type = "futures/um"
        rest = base_name.replace("futures_um_", "")
    elif "futures_cm_" in base_name:
        market_type = "futures/cm"
        rest = base_name.replace("futures_cm_", "")
    else:
        rest = base_name

    # 分割频率和数据类型
    parts = rest.split("_")
    if len(parts) >= 2:
        # 第一部分应该是频率
        if parts[0] == "daily":
            data_frequency = "daily"
        elif parts[0] == "monthly":
            data_frequency = "monthly"

        # 第二部分应该是数据类型
        data_type = parts[1]
    elif len(parts) == 1:
        # 只有一部分，假设是数据类型
        data_type = parts[0]

    print(f"DEBUG: 文件名={file_name}, 解析结果: 市场={market_type}, 频率={data_frequency}, 数据类型={data_type}")

    return market_type, data_frequency, data_type


def verify_single_file(file_name, batch_mode=False):
    """验证单个文件

    Args:
        file_name: 要验证的文件名
        batch_mode: 是否为批量模式，默认为False
    """
    # 构建完整文件路径
    file_path = os.path.join(output_dir, file_name)

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在!")
        return

    # 从文件名解析市场类型、数据频率和数据类型
    market_type, data_frequency, data_type = parse_file_info(file_name)
    print(f"解析文件信息: 市场={market_type}, 频率={data_frequency}, 数据类型={data_type}")

    # 加载结果文件
    print(f"正在加载结果文件: {file_path}")
    content = load_results(file_path)

    if not content:
        print("未能成功加载结果文件!")
        return

    # 解析结果
    print("正在解析结果...")
    pairs_data = parse_results(content)

    # 与网站数据比较验证结果
    return verify_results_with_website(pairs_data, file_name, market_type, data_type, data_frequency, batch_mode)


def verify_all_files(batch_mode=True):
    """验证目录下所有的txt文件

    Args:
        batch_mode: 是否为批量模式，默认为True
    """
    # 获取目录下所有数据文件
    all_files = []
    for f in os.listdir(output_dir):
        # 检查是否是数据文件
        if f.endswith("_all_links.txt"):
            all_files.append(f)
        # 添加其他可能的数据文件后缀
        elif f.endswith(".txt") and ("_links" in f or "_data" in f):
            all_files.append(f)

    # 按文件名排序，便于查看
    all_files.sort()

    if not all_files:
        print(f"在 {output_dir} 目录下没有找到任何 _all_links.txt 文件")
        return

    print(f"找到 {len(all_files)} 个结果文件需要验证")

    # 获取配置中的数据频率，如果没有则使用默认值"monthly"
    data_frequency = config.get("data_frequency", "monthly")
    print(f"使用数据频率: {data_frequency}")

    # 创建汇总报告
    summary_report = {
        "total_files": len(all_files),
        "verification_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "data_frequency": data_frequency,  # 在汇总报告中包含数据频率信息
        "file_reports": [],
        "missing_pairs": {
            "spot": {
                "klines": [],
                "trades": [],
                "aggTrades": [],
                "bookTicker": [],
                "bookDepth": []
            },
            "futures/um": {
                "klines": [],
                "trades": [],
                "aggTrades": [],
                "bookTicker": [],
                "indexPriceKlines": [],
                "markPriceKlines": [],
                "premiumIndexKlines": [],
                "fundingRate": [],
                "bookDepth": [],
                "metrics": [],
                "liquidationSnapshot": []
            },
            "futures/cm": {
                "klines": [],
                "trades": [],
                "aggTrades": [],
                "bookTicker": [],
                "indexPriceKlines": [],
                "markPriceKlines": [],
                "premiumIndexKlines": [],
                "fundingRate": [],
                "bookDepth": [],
                "metrics": [],
                "liquidationSnapshot": []
            },
            "option": {
                "BVOLIndex": [],
                "EOHSummary": []
            }
        },
        "zero_data_pairs": {
            "spot": {
                "klines": [],
                "trades": [],
                "aggTrades": [],
                "bookTicker": [],
                "bookDepth": []
            },
            "futures/um": {
                "klines": [],
                "trades": [],
                "aggTrades": [],
                "bookTicker": [],
                "indexPriceKlines": [],
                "markPriceKlines": [],
                "premiumIndexKlines": [],
                "fundingRate": [],
                "bookDepth": [],
                "metrics": [],
                "liquidationSnapshot": []
            },
            "futures/cm": {
                "klines": [],
                "trades": [],
                "aggTrades": [],
                "bookTicker": [],
                "indexPriceKlines": [],
                "markPriceKlines": [],
                "premiumIndexKlines": [],
                "fundingRate": [],
                "bookDepth": [],
                "metrics": [],
                "liquidationSnapshot": []
            },
            "option": {
                "BVOLIndex": [],
                "EOHSummary": []
            }
        }
    }

    # 逐个验证文件
    for i, file_name in enumerate(all_files):
        print(f"\n[{i+1}/{len(all_files)}] 正在验证: {file_name}")

        # 构建完整文件路径
        file_path = os.path.join(output_dir, file_name)

        # 从文件名解析市场类型、数据频率和数据类型
        market_type, data_frequency, data_type = parse_file_info(file_name)
        print(f"解析文件信息: 市场={market_type}, 频率={data_frequency}, 数据类型={data_type}")

        # 加载结果文件
        print(f"正在加载结果文件: {file_path}")
        content = load_results(file_path)

        if not content:
            print("未能成功加载结果文件!")
            continue

        # 解析结果
        print("正在解析结果...")
        pairs_data = parse_results(content)

        # 与网站数据比较验证结果
        report = verify_results_with_website(pairs_data, file_name, market_type, data_type, data_frequency, batch_mode)

        # 添加到汇总报告
        if report:
            file_summary = {
                "file_name": file_name,
                "market_type": market_type,
                "data_type": data_type,
                "data_frequency": data_frequency,
                "completeness_rate": report.get("completeness_rate", 0),
                "missing_pairs_count": report.get("missing_pairs_count", 0),
                "crawled_pairs_count": report.get("crawled_pairs_count", 0),
                "website_pairs_count": report.get("website_pairs_count", 0),
                "zero_data_pairs_count": len(report.get("zero_data_pairs", []))
            }
            summary_report["file_reports"].append(file_summary)

            # 收集缺失的币对并按市场和数据类型分类
            if "missing_pairs" in report and report["missing_pairs"]:
                # 确保市场类型存在
                if market_type not in summary_report["missing_pairs"]:
                    summary_report["missing_pairs"][market_type] = {}
                
                # 确保数据类型存在
                if data_type not in summary_report["missing_pairs"][market_type]:
                    summary_report["missing_pairs"][market_type][data_type] = []
                
                # 将缺失的币对添加到对应的分类中
                for pair in report["missing_pairs"]:
                    if pair not in summary_report["missing_pairs"][market_type][data_type]:
                        summary_report["missing_pairs"][market_type][data_type].append(pair)
            
            # 收集存在但没有数据的币对
            if "zero_data_pairs" in report and report["zero_data_pairs"]:
                # 确保市场类型存在
                if market_type not in summary_report["zero_data_pairs"]:
                    summary_report["zero_data_pairs"][market_type] = {}
                
                # 确保数据类型存在
                if data_type not in summary_report["zero_data_pairs"][market_type]:
                    summary_report["zero_data_pairs"][market_type][data_type] = []
                
                # 将零数据币对添加到对应的分类中
                for pair in report["zero_data_pairs"]:
                    if pair not in summary_report["zero_data_pairs"][market_type][data_type]:
                        summary_report["zero_data_pairs"][market_type][data_type].append(pair)

    # 创建保存新增数据的目录结构
    new_data_dir = "new_data"
    if not os.path.exists(new_data_dir):
        os.makedirs(new_data_dir)
    
    # 确保每个市场类型和数据类型都有对应的子目录
    for market_type in summary_report["missing_pairs"]:
        market_dir = os.path.join(new_data_dir, market_type.replace("/", "_"))
        if not os.path.exists(market_dir):
            os.makedirs(market_dir)
        
        for data_type in summary_report["missing_pairs"][market_type]:
            if summary_report["missing_pairs"][market_type][data_type]:  # 只为有缺失数据的类型创建目录
                data_type_dir = os.path.join(market_dir, data_type)
                if not os.path.exists(data_type_dir):
                    os.makedirs(data_type_dir)
    
    # 为零数据币对也创建目录
    for market_type in summary_report["zero_data_pairs"]:
        market_dir = os.path.join(new_data_dir, market_type.replace("/", "_"))
        if not os.path.exists(market_dir):
            os.makedirs(market_dir)
        
        for data_type in summary_report["zero_data_pairs"][market_type]:
            if summary_report["zero_data_pairs"][market_type][data_type]:  # 只为有零数据币对的类型创建目录
                data_type_dir = os.path.join(market_dir, data_type)
                if not os.path.exists(data_type_dir):
                    os.makedirs(data_type_dir)

    # 生成报告文件名，使用日期和时间戳避免覆盖
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"verification_summary_{timestamp}.json"
    log_file = f"verification_log_{timestamp}.txt"

    # 保存汇总报告 - 这是唯一的JSON文件
    with open(os.path.join(output_dir, summary_file), "w") as f:
        json.dump(summary_report, f, indent=2, ensure_ascii=False)

    # 生成汇总的缺失币对列表文件
    summary_txt_file = os.path.join(new_data_dir, f"missing_pairs_summary_{timestamp}.txt")
    with open(summary_txt_file, "w") as f:
        f.write(f"===== 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} =====\n\n")
        
        # 计算总缺失币对数量和零数据币对数量
        total_missing = 0
        total_zero_data = 0
        for market in summary_report["missing_pairs"]:
            for data_type, pairs in summary_report["missing_pairs"][market].items():
                total_missing += len(pairs)
        
        for market in summary_report["zero_data_pairs"]:
            for data_type, pairs in summary_report["zero_data_pairs"][market].items():
                total_zero_data += len(pairs)
        
        f.write(f"总缺失币对数量: {total_missing}\n")
        f.write(f"零数据币对数量: {total_zero_data}\n\n")
        f.write(f"===== 按市场和数据类型分类的缺失交易对 =====\n\n")

        # 按市场和数据类型输出缺失的交易对
        for market in summary_report["missing_pairs"]:
            market_missing = sum(len(pairs) for pairs in summary_report["missing_pairs"][market].values())
            if market_missing > 0:  # 只输出有缺失币对的市场
                f.write(f"\n=== {market} ({market_missing}) ===\n")
                for data_type, pairs in summary_report["missing_pairs"][market].items():
                    if pairs:  # 只输出有缺失币对的数据类型
                        f.write(f"\n--- {data_type} ({len(pairs)}) ---\n")
                        for pair in sorted(pairs):
                            f.write(f"{pair}\n")
        
                        # 为每个有缺失币对的数据类型创建单独的文件
                        market_str = market.replace("/", "_")
                        missing_file = os.path.join(new_data_dir, market_str, data_type, f"missing_pairs_{timestamp}.txt")
                        os.makedirs(os.path.dirname(missing_file), exist_ok=True)
                        with open(missing_file, "w") as mf:
                            mf.write(f"===== {market} - {data_type} 缺失币对 =====\n")
                            mf.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            mf.write(f"市场类型: {market}\n")
                            mf.write(f"数据类型: {data_type}\n")
                            # 对于特定数据类型自动使用daily频率
                            current_frequency = "daily" if data_type in ["metrics", "liquidationSnapshot"] else data_frequency
                            mf.write(f"数据频率: {current_frequency}\n\n")
                            for pair in sorted(pairs):
                                mf.write(f"{pair}\n")
        
        # 输出零数据币对
        f.write(f"\n\n===== 按市场和数据类型分类的零数据交易对 =====\n\n")
        for market in summary_report["zero_data_pairs"]:
            market_zero_data = sum(len(pairs) for pairs in summary_report["zero_data_pairs"][market].values())
            if market_zero_data > 0:  # 只输出有零数据币对的市场
                f.write(f"\n=== {market} ({market_zero_data}) ===\n")
                for data_type, pairs in summary_report["zero_data_pairs"][market].items():
                    if pairs:  # 只输出有零数据币对的数据类型
                        f.write(f"\n--- {data_type} ({len(pairs)}) ---\n")
                        for pair in sorted(pairs):
                            f.write(f"{pair}\n")
                        
                        # 为每个有零数据币对的数据类型创建单独的文件
                        market_str = market.replace("/", "_")
                        zero_data_file = os.path.join(new_data_dir, market_str, data_type, f"zero_data_pairs_{timestamp}.txt")
                        with open(zero_data_file, "w") as zf:
                            zf.write(f"===== {market} - {data_type} 零数据币对 =====\n")
                            zf.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            zf.write(f"市场类型: {market}\n")
                            zf.write(f"数据类型: {data_type}\n")
                            # 对于特定数据类型自动使用daily频率
                            current_frequency = "daily" if data_type in ["metrics", "liquidationSnapshot"] else data_frequency
                            zf.write(f"数据频率: {current_frequency}\n\n")
                            for pair in sorted(pairs):
                                zf.write(f"{pair}\n")

    # 生成自动运行批处理脚本
    batch_script = os.path.join(new_data_dir, f"run_supplementary_{timestamp}.bat")
    with open(batch_script, "w") as bs:
        bs.write("@echo off\n")
        bs.write("echo 开始自动补充缺失数据\n\n")
        
        # 处理缺失的币对
        for market in summary_report["missing_pairs"]:
            market_str = market.replace("/", "_")
            for data_type, pairs in summary_report["missing_pairs"][market].items():
                if pairs:  # 只为有缺失币对的数据类型创建命令
                    missing_file = os.path.join(new_data_dir, market_str, data_type, f"missing_pairs_{timestamp}.txt")
                    bs.write(f'echo 处理 {market} - {data_type} 的缺失币对\n')
                    # 确保批处理脚本明确指定了数据频率
                    # 对于特定数据类型自动使用daily频率
                    current_frequency = "daily" if data_type in ["metrics", "liquidationSnapshot"] else data_frequency
                    bs.write(f'python supplementary_crawler.py --file "{missing_file}" --market "{market}" --data-type "{data_type}" --frequency "{current_frequency}"\n')
                    bs.write('echo.\n\n')
        
        # 处理零数据币对
        for market in summary_report["zero_data_pairs"]:
            market_str = market.replace("/", "_")
            for data_type, pairs in summary_report["zero_data_pairs"][market].items():
                if pairs:  # 只为有零数据币对的数据类型创建命令
                    zero_data_file = os.path.join(new_data_dir, market_str, data_type, f"zero_data_pairs_{timestamp}.txt")
                    bs.write(f'echo 处理 {market} - {data_type} 的零数据币对\n')
                    # 确保批处理脚本明确指定了数据频率
                    # 对于特定数据类型自动使用daily频率
                    current_frequency = "daily" if data_type in ["metrics", "liquidationSnapshot"] else data_frequency
                    bs.write(f'python supplementary_crawler.py --file "{zero_data_file}" --market "{market}" --data-type "{data_type}" --frequency "{current_frequency}"\n')
                    bs.write('echo.\n\n')
        
        bs.write("echo 所有补充任务已完成\n")
        bs.write("pause\n")

    # 保存日志文件，包含分类后的缺失交易对
    with open(os.path.join(output_dir, log_file), "w") as f:
        f.write(f"\n===== 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} =====\n\n")
        f.write(f"\n===== 按市场和数据类型分类的缺失交易对 =====\n\n")

        # 按市场和数据类型输出缺失的交易对
        for market in summary_report["missing_pairs"]:
            market_missing = sum(len(pairs) for pairs in summary_report["missing_pairs"][market].values())
            if market_missing > 0:  # 只输出有缺失币对的市场
                f.write(f"\n=== {market} ===\n")
                for data_type, pairs in summary_report["missing_pairs"][market].items():
                    if pairs:  # 只输出有缺失币对的数据类型
                        f.write(f"\n--- {data_type} ---\n")
                        for pair in sorted(pairs):
                            f.write(f"{pair}\n")
        
        # 输出零数据币对
        f.write(f"\n\n===== 按市场和数据类型分类的零数据交易对 =====\n\n")
        for market in summary_report["zero_data_pairs"]:
            market_zero_data = sum(len(pairs) for pairs in summary_report["zero_data_pairs"][market].values())
            if market_zero_data > 0:  # 只输出有零数据币对的市场
                f.write(f"\n=== {market} ===\n")
                for data_type, pairs in summary_report["zero_data_pairs"][market].items():
                    if pairs:  # 只输出有零数据币对的数据类型
                        f.write(f"\n--- {data_type} ---\n")
                        for pair in sorted(pairs):
                            f.write(f"{pair}\n")

    print(f"\n所有文件验证完成，汇总报告已保存到: {os.path.join(output_dir, summary_file)}")
    print(f"分类后的缺失交易对和零数据币对已记录在日志文件: {os.path.join(output_dir, log_file)}")
    print(f"缺失币对和零数据币对汇总文件已保存到: {summary_txt_file}")
    print(f"自动补充脚本已生成: {batch_script}")

    # 返回汇总报告文件路径和日志文件路径
    return os.path.join(output_dir, summary_file), os.path.join(output_dir, log_file)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="币安数据完整性验证工具")

    # 模式选择
    parser.add_argument("--mode", "-m", type=str, choices=["single", "all"],
                        default=DEFAULT_CONFIG["mode"],
                        help="验证模式: single(单文件), all(所有文件)")

    # 文件名参数（单文件模式下使用）
    parser.add_argument("--file", "-f", type=str,
                        default=DEFAULT_CONFIG["file"],
                        help="要验证的文件名（单文件模式下使用）")

    # 市场类型参数（可选，如果指定则覆盖从文件名解析的值）
    parser.add_argument("--market", type=str,
                        choices=["spot", "futures/um", "futures/cm"],
                        help="市场类型: spot(现货), futures/um(U本位合约), futures/cm(币本位合约)")

    # 数据类型参数（可选）
    parser.add_argument("--data-type", type=str,
                        help="数据类型: klines, trades, aggTrades 等")

    # 数据频率参数（可选）
    parser.add_argument("--frequency", type=str,
                        choices=["daily", "monthly"],
                        default="monthly",
                        help="数据频率: daily(每日数据), monthly(月度数据)")

    # 批量模式参数
    parser.add_argument("--batch", "-b", action="store_true",
                        help="批量模式，只生成一个汇总报告，而不是为每个文件生成单独的报告")

    args = parser.parse_args()

    # 更新全局配置
    config["mode"] = args.mode
    config["file"] = args.file
    config["batch_mode"] = args.batch
    config["data_frequency"] = args.frequency

    # 如果指定了市场类型、数据类型或数据频率，则更新配置
    if args.market:
        config["market_type"] = args.market
    if args.data_type:
        config["data_type"] = args.data_type
    if args.frequency:
        config["data_frequency"] = args.frequency

    return config


def main():
    # 解析命令行参数
    config = parse_arguments()

    # 检查输出目录是否存在
    if not os.path.exists(output_dir):
        print(f"输出目录 {output_dir} 不存在!")
        return

    # 根据模式选择验证方式
    batch_mode = config.get("batch_mode", False)
    if batch_mode:
        print("批量模式已启用，只生成一个汇总报告")
        # 删除现有的报告文件，避免生成大量文件
        try:
            # 删除完整性报告文件
            for f in os.listdir(output_dir):
                if f.startswith("completeness_report_") and f.endswith(".json"):
                    try:
                        os.remove(os.path.join(output_dir, f))
                        print(f"删除旧报告文件: {f}")
                    except Exception as e:
                        print(f"无法删除文件 {f}: {e}")

                # 删除验证报告文件
                if f.startswith("verification_report_") and f.endswith(".json"):
                    try:
                        os.remove(os.path.join(output_dir, f))
                        print(f"删除旧报告文件: {f}")
                    except Exception as e:
                        print(f"无法删除文件 {f}: {e}")
        except Exception as e:
            print(f"清理旧报告文件时出错: {e}")

    if config["mode"] == "single":
        print(f"单文件模式: 验证文件 {config['file']}")
        verify_single_file(config["file"], batch_mode)
    else:  # all模式
        print("批量模式: 验证目录下所有结果文件")
        result = verify_all_files(batch_mode)
        if result:
            if isinstance(result, tuple) and len(result) >= 2:
                summary_file, log_file = result
                print(f"汇总报告已生成: {summary_file}")
                print(f"分类后的缺失交易对日志: {log_file}")
            else:
                summary_file = result
                print(f"汇总报告已生成: {summary_file}")


if __name__ == "__main__":
    main()