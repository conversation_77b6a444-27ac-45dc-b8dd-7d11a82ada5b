import os
import time
import re
import json
import logging
import sys
import argparse
from datetime import datetime
from tqdm import tqdm  # 导入进度条库
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, StaleElementReferenceException, NoSuchElementException

# 设置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 初始化日志器
logger = logging.getLogger(__name__)

# 防止多次配置
_logging_configured = False

# 日志配置函数
def setup_logging(batch_mode=False, log_to_file=True):
    """设置日志配置

    Args:
        batch_mode: 是否为批量模式，默认为False
        log_to_file: 是否输出日志到文件，默认为True
    """
    global logger, _logging_configured

    # 如果已经配置过日志，则跳过
    if _logging_configured:
        return None

    # 标记为已配置
    _logging_configured = True

    # 如果不输出日志到文件，直接返回
    if not log_to_file:
        # 只配置控制台输出
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
        logger.addHandler(console_handler)
        logger.setLevel(logging.INFO if not batch_mode else logging.WARNING)

        # 屏蔽selenium内部日志
        logging.getLogger('selenium').setLevel(logging.ERROR)
        logging.getLogger('urllib3').setLevel(logging.ERROR)

        return None

    # 如果是批量模式，使用固定的日志文件名
    if batch_mode:
        log_file = os.path.join(log_dir, "supplementary_crawler_batch.log")
        # 如果文件已存在，先清空它
        if os.path.exists(log_file):
            open(log_file, 'w').close()
    else:
        # 如果不是批量模式，使用带时间戳的日志文件名
        log_file = os.path.join(log_dir, f"supplementary_crawler_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 配置日志处理器
    handlers = [
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file, encoding='utf-8')
    ]

    # 配置日志
    for handler in handlers:
        handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
        logger.addHandler(handler)

    logger.setLevel(logging.INFO if not batch_mode else logging.WARNING)

    # 屏蔽selenium内部日志
    logging.getLogger('selenium').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)

    return log_file

# Chrome浏览器路径
CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

# 基础URL
BASE_URL = "https://data.binance.vision/"

# 保存链接的目录
output_dir = "binance_links"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 最大重试次数
MAX_RETRIES = 5
# 页面加载超时时间(秒)
PAGE_LOAD_TIMEOUT = 180

def setup_visible_browser():
    """设置可见的Chrome浏览器"""
    chrome_options = Options()
    chrome_options.binary_location = CHROME_PATH

    # 不使用无头模式，让浏览器可见
    # 添加其他必要选项
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--start-maximized")

    # 禁用日志输出到控制台，避免USB设备等警告
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

    # 创建WebDriver
    logger.info("启动Chrome浏览器...")
    driver = webdriver.Chrome(options=chrome_options)
    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    return driver

def wait_for_page_load(driver, timeout=90):
    """等待页面完全加载"""
    start_time = time.time()
    last_page_source = ""
    stable_count = 0
    min_stable_time = 2  # 最小稳定时间(秒)
    
    while time.time() - start_time < timeout:
        # 检查readyState
        page_state = driver.execute_script('return document.readyState;')
        
        # 获取当前页面源码
        current_page_source = driver.page_source
        
        # 如果页面状态为complete且页面源码连续两次没有变化，认为页面已加载完成
        if page_state == 'complete':
            if current_page_source == last_page_source:
                stable_count += 1
                if stable_count >= 3 and (time.time() - start_time) >= min_stable_time:  # 连续三次页面内容相同且至少已经等待最小稳定时间
                    logger.info(f"页面内容已稳定，耗时: {time.time() - start_time:.2f}秒")
                    return True
            else:
                stable_count = 0
                
        last_page_source = current_page_source
        time.sleep(1)  # 增加检查间隔，给页面更多加载时间
        
    raise TimeoutException(f"页面在 {timeout} 秒内未完成加载")

def load_page_with_retry(driver, url, max_retries=MAX_RETRIES):
    """带重试机制的页面加载"""
    for attempt in range(max_retries):
        try:
            logger.info(f"尝试加载页面 (尝试 {attempt+1}/{max_retries}): {url}")
            driver.get(url)

            # 确保页面完全加载
            wait_for_page_load(driver)

            # 检查页面是否有内容
            page_html = driver.execute_script("return document.documentElement.outerHTML;")
            page_length = len(page_html)
            logger.info(f"页面已加载，HTML长度: {page_length} 字符")
            
            # 只有页面内容太小时才等待额外时间
            if page_length < 2000:
                logger.info("页面内容较少，等待10秒确保完全加载...")
                time.sleep(10)
                
                # 重新获取页面内容，确认是否有变化
                new_page_html = driver.execute_script("return document.documentElement.outerHTML;")
                new_page_length = len(new_page_html)
                
                if new_page_length > page_length:
                    logger.info(f"等待后页面内容增加，当前HTML长度: {new_page_length} 字符")
                elif new_page_length < 2000:
                    logger.warning(f"页面内容仍然较少，可能未完全加载，尝试刷新...")
                    driver.refresh()
                    wait_for_page_load(driver)
            else:
                logger.info("页面内容已加载，继续处理...")

            return True
        except (TimeoutException, WebDriverException) as e:
            if attempt < max_retries - 1:
                logger.warning(f"页面加载超时或出错: {e}")
                logger.info(f"等待5秒后重试...")
                time.sleep(5)
            else:
                logger.error(f"页面加载失败，已达到最大重试次数: {url}")
                return False
    return False

def force_refresh_and_retry(driver, url, stage="未知阶段"):
    """强制刷新并重试加载页面"""
    logger.info(f"强制刷新页面并重试 ({stage})...")
    try:
        driver.refresh()
        # 等待页面加载完成
        wait_for_page_load(driver)
        
        # 检查内容
        page_html = driver.execute_script("return document.documentElement.outerHTML;")
        page_length = len(page_html)
        logger.info(f"刷新后页面HTML长度: {page_length} 字符")
        
        # 如果刷新后内容不足，尝试直接访问URL
        if page_length < 2000:
            logger.info("刷新后内容不足，尝试直接访问URL...")
            driver.get(url)
            wait_for_page_load(driver)
            
            # 再次检查内容
            page_html = driver.execute_script("return document.documentElement.outerHTML;")
            page_length = len(page_html)
            logger.info(f"直接访问后页面HTML长度: {page_length} 字符")
            
            # 如果内容仍然不足，等待额外时间
            if page_length < 2000:
                logger.info("内容仍然不足，等待额外10秒...")
                time.sleep(10)
        
        return True
    except Exception as e:
        logger.error(f"强制刷新失败: {e}")
        return False

def get_data_files(driver, pair_name, market_type="futures/um", data_type="klines", interval="1d", data_frequency="monthly"):
    """获取特定币对的数据文件链接
    
    使用动态等待机制而不是固定时间等待，通过检测页面内容稳定性确定页面是否加载完成。

    Args:
        driver: WebDriver实例
        pair_name: 币对名称
        market_type: 市场类型，默认为"futures/um"
        data_type: 数据类型，默认为"klines"
        interval: 时间周期，默认为"1d"，对非K线数据无效
        data_frequency: 数据频率，默认为"monthly"

    Returns:
        数据文件链接列表
    """
    # 构建URL，根据数据类型决定是否包含时间周期
    if data_type == "klines" or data_type.endswith("Klines"):
        # K线数据需要时间周期
        pair_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair_name}/{interval}/"
        logger.info(f"正在访问 {pair_name} 的 {data_type}/{interval} 数据页面: {pair_url}")
    else:
        # 非K线数据不需要时间周期
        pair_url = f"{BASE_URL}?prefix=data/{market_type}/{data_frequency}/{data_type}/{pair_name}/"
        logger.info(f"正在访问 {pair_name} 的 {data_type} 数据页面: {pair_url}")

    # 带重试机制加载页面
    if not load_page_with_retry(driver, pair_url):
        logger.warning(f"无法加载 {pair_name} 的数据页面，尝试强制刷新...")
        if not force_refresh_and_retry(driver, pair_url, f"{pair_name} 数据页面"):
            # 如果强制刷新失败，尝试第二次加载
            logger.warning(f"强制刷新失败，尝试直接访问页面...")
            if not load_page_with_retry(driver, pair_url):
                logger.error(f"多次尝试后仍无法加载页面: {pair_url}")
                return []

    # 检查页面是否有内容并包含预期的元素
    page_html = driver.execute_script("return document.documentElement.outerHTML;")
    page_length = len(page_html)
    
    # 检查页面是否包含预期的表格或链接元素
    has_table = len(driver.find_elements(By.TAG_NAME, "table")) > 0
    has_links = len(driver.find_elements(By.TAG_NAME, "a")) > 0
    
    # 只有当页面大小合适但未检测到关键元素时，才额外等待
    if page_length < 10000 and (not has_table or not has_links):
        wait_time = 3
        logger.info(f"页面已稳定但可能尚未完全加载（表格: {has_table}, 链接: {has_links}），等待{wait_time}秒确保JavaScript执行完毕...")
        time.sleep(wait_time)

    # 使用多种方法尝试获取数据文件
    attempts = [
        ("JavaScript", get_files_with_js, driver),
        ("页面源码", get_files_from_source, driver),
        ("表格解析", get_files_from_table, driver),
        ("XPath", get_files_with_xpath, driver),
        ("所有链接", get_files_from_all_links, driver)
    ]

    for method_name, method_func, driver in attempts:
        try:
            logger.info(f"  尝试使用{method_name}方法获取数据文件...")
            files = method_func(driver)
            if files:
                logger.info(f"  使用{method_name}方法找到 {len(files)} 个数据文件")
                return files
        except Exception as e:
            logger.warning(f"  使用{method_name}方法获取文件失败: {e}")

    # 如果所有方法都失败，尝试最后一次强制刷新
    logger.warning(f"  使用所有方法都无法获取 {pair_name} 的数据文件，尝试最后一次强制刷新")
    if force_refresh_and_retry(driver, pair_url, f"{pair_name} 最后尝试"):
        # 仅当页面看起来不完整时才等待额外时间
        if len(driver.find_elements(By.TAG_NAME, "table")) == 0 or len(driver.find_elements(By.TAG_NAME, "a")) < 5:
            logger.info(f"  强制刷新后页面元素不足，等待5秒确保完全加载...")
            time.sleep(5)
        
        # 再次尝试所有方法
        for method_name, method_func, driver in attempts:
            try:
                logger.info(f"  最后尝试: 使用{method_name}方法获取数据文件...")
                files = method_func(driver)
                if files:
                    logger.info(f"  成功: 使用{method_name}方法找到 {len(files)} 个数据文件")
                    return files
            except Exception as e:
                logger.warning(f"  最后尝试: 使用{method_name}方法获取文件失败: {e}")

    logger.warning(f"  多次尝试后仍无法获取 {pair_name} 的数据文件")
    return []

def get_files_with_js(driver):
    """使用JavaScript获取文件链接"""
    page_html = driver.execute_script("return document.documentElement.outerHTML;")
    pattern = r'href="([^"]+\.zip)"'
    matches = re.findall(pattern, page_html)

    files = []
    for file_path in matches:
        # 构建完整URL
        if not file_path.startswith('http'):
            file_url = f"{BASE_URL}{file_path}" if not file_path.startswith('/') else f"{BASE_URL}/{file_path}"
        else:
            file_url = file_path
        files.append(file_url)

    return files

def get_files_from_source(driver):
    """从页面源码获取文件链接"""
    page_source = driver.page_source
    pattern = r'href="([^"]+\.zip)"'
    matches = re.findall(pattern, page_source)

    files = []
    for file_path in matches:
        if not file_path.startswith('http'):
            file_url = f"{BASE_URL}{file_path}" if not file_path.startswith('/') else f"{BASE_URL}/{file_path}"
        else:
            file_url = file_path
        files.append(file_url)

    return files

def get_files_from_table(driver):
    """从表格中获取文件链接"""
    tables = driver.find_elements(By.TAG_NAME, "table")

    all_files = []

    # 遍历所有表格
    for table in tables:
        try:
            rows = table.find_elements(By.TAG_NAME, "tr")

            for row in rows:
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) >= 1:
                        links = cells[0].find_elements(By.TAG_NAME, "a")
                        for link in links:
                            href = link.get_attribute("href")
                            text = link.text.strip()
                            if text and text.endswith('.zip'):
                                all_files.append(href)
                except:
                    continue
        except:
            continue

    return all_files

def get_files_with_xpath(driver):
    """使用XPath获取文件链接"""
    zip_links = driver.find_elements(By.XPATH, "//a[contains(@href, '.zip')]")
    return [link.get_attribute("href") for link in zip_links if link.get_attribute("href")]

def get_files_from_all_links(driver):
    """从所有链接中查找文件链接"""
    all_links = driver.find_elements(By.TAG_NAME, "a")
    files = []
    for link in all_links:
        try:
            href = link.get_attribute("href")
            if href and href.endswith('.zip'):
                files.append(href)
        except:
            continue
    return files

def load_existing_results(data_type="klines", market_type="futures/um", data_frequency="monthly"):
    """加载现有的结果文件

    Args:
        data_type: 数据类型，默认为"klines"
        market_type: 市场类型，默认为"futures/um"
        data_frequency: 数据频率，默认为"monthly"

    Returns:
        加载的数据字典
    """
    # 根据数据类型和市场类型生成文件名
    market_str = market_type.replace("/", "_")
    results_file = os.path.join(output_dir, f"{market_str}_{data_frequency}_{data_type}_all_links.txt")
    
    # 如果指定的文件不存在，并且数据频率是monthly，则尝试使用daily频率
    if not os.path.exists(results_file) and data_frequency == "monthly" and data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
        daily_file = os.path.join(output_dir, f"{market_str}_daily_{data_type}_all_links.txt")
        if os.path.exists(daily_file):
            logger.warning(f"指定的monthly结果文件不存在，但找到对应的daily文件: {daily_file}")
            results_file = daily_file
            data_frequency = "daily"
    
    # 如果指定的文件不存在，并且数据频率是daily，则尝试使用monthly频率
    if not os.path.exists(results_file) and data_frequency == "daily" and data_type not in ["metrics", "liquidationSnapshot", "bookDepth"]:
        monthly_file = os.path.join(output_dir, f"{market_str}_monthly_{data_type}_all_links.txt")
        if os.path.exists(monthly_file):
            logger.warning(f"指定的daily结果文件不存在，但找到对应的monthly文件: {monthly_file}")
            results_file = monthly_file
            data_frequency = "monthly"

    # 如果指定的文件不存在，尝试加载默认文件
    if not os.path.exists(results_file):
        default_file = os.path.join(output_dir, "all_links.txt")
        if os.path.exists(default_file):
            logger.warning(f"指定的结果文件 {results_file} 不存在，尝试加载默认文件 {default_file}")
            results_file = default_file
        else:
            logger.warning(f"结果文件 {results_file} 不存在，将创建新文件")
            return {}

    try:
        with open(results_file, 'r') as f:
            content = f.read()

        # 使用正则表达式分割币对部分
        pair_sections = re.split(r'===\s*([^=]+)\s*===', content)

        # 去掉第一个空元素
        if pair_sections and not pair_sections[0].strip():
            pair_sections = pair_sections[1:]

        pairs_data = {}

        # 每两个元素为一组 (币对名称, 链接列表)
        for i in range(0, len(pair_sections), 2):
            if i + 1 < len(pair_sections):
                pair_name = pair_sections[i].strip()
                content_text = pair_sections[i + 1].strip()

                # 检查是否包含时间周期标记
                interval_sections = re.split(r'---\s*([^-]+)\s*---', content_text)

                if len(interval_sections) > 1:  # 包含时间周期
                    # 去掉第一个空元素
                    if interval_sections and not interval_sections[0].strip():
                        interval_sections = interval_sections[1:]

                    # 创建嵌套字典
                    pairs_data[pair_name] = {}

                    # 每两个元素为一组 (时间周期, 链接列表)
                    for j in range(0, len(interval_sections), 2):
                        if j + 1 < len(interval_sections):
                            interval = interval_sections[j].strip()
                            links_text = interval_sections[j + 1].strip()
                            links = [line.strip() for line in links_text.split('\n') if line.strip()]
                            pairs_data[pair_name][interval] = links
                else:  # 不包含时间周期
                    links = [line.strip() for line in content_text.split('\n') if line.strip()]
                    pairs_data[pair_name] = links

        logger.info(f"成功加载现有结果文件，包含 {len(pairs_data)} 个币对")
        return pairs_data

    except Exception as e:
        logger.error(f"加载现有结果文件时出错: {e}")
        return {}

def save_results(all_links, data_type="klines", market_type="futures/um", data_frequency="monthly"):
    """保存结果到文件

    Args:
        all_links: 所有链接的字典
        data_type: 数据类型，默认为"klines"
        market_type: 市场类型，默认为"futures/um"
        data_frequency: 数据频率，默认为"monthly"
    """
    # 根据数据类型和市场类型生成文件名
    market_str = market_type.replace("/", "_")
    results_file = os.path.join(output_dir, f"{market_str}_{data_frequency}_{data_type}_all_links.txt")

    with open(results_file, "w") as f:
        for pair_name, data in all_links.items():
            # 检查是否是嵌套字典（带时间周期的K线数据）
            if isinstance(data, dict):
                # 如果是嵌套字典，则按时间周期分组
                f.write(f"=== {pair_name} ===\n")
                for interval, links in data.items():
                    f.write(f"--- {interval} ---\n")
                    for link in links:
                        f.write(f"{link}\n")
                    f.write("\n")
            else:
                # 如果是普通列表，直接写入链接
                f.write(f"=== {pair_name} ===\n")
                for link in data:
                    f.write(f"{link}\n")
                f.write("\n")

    logger.info(f"结果已保存到: {results_file}")
    return results_file

def process_missing_pairs(missing_pairs, driver=None, existing_data=None, market_type="futures/um", data_type="klines", interval="1d", data_frequency="monthly", batch_mode=False):
    """处理缺失的交易对数据

    Args:
        missing_pairs: 缺失的币对列表，可以是字符串列表或元组列表（币对名称，时间周期）
        driver: WebDriver实例，如果为None则创建新的
        existing_data: 现有的数据字典，如果为None则加载现有结果
        market_type: 市场类型，默认为"futures/um"
        data_type: 数据类型，默认为"klines"
        interval: 时间周期，默认为"1d"，对非K线数据无效
        data_frequency: 数据频率，默认为"monthly"
        batch_mode: 是否为批量模式，默认为False

    Returns:
        新增的数据字典
    """
    if not missing_pairs:
        logger.info("没有需要补充的币对数据")
        return {}

    # 检查missing_pairs的格式，判断是否包含时间周期
    has_intervals = False
    if missing_pairs and isinstance(missing_pairs[0], tuple) and len(missing_pairs[0]) >= 2:
        has_intervals = True
        logger.info(f"开始补充爬取 {len(missing_pairs)} 个带时间周期的缺失币对数据...")
    else:
        logger.info(f"开始补充爬取 {len(missing_pairs)} 个缺失的币对数据...")

    # 如果没有传入driver，创建一个新的
    close_driver = False
    if driver is None:
        driver = setup_visible_browser()
        close_driver = True

    # 加载现有结果（如果没有传入）
    if existing_data is None:
        existing_data = load_existing_results(data_type, market_type, data_frequency)

    try:
        # 记录成功和失败的币对
        success_pairs = []
        failed_pairs = []
        new_data = {}

        # 使用tqdm创建进度条
        progress_bar = tqdm(missing_pairs, desc=f"补充爬取{data_type}币对", ncols=100)

        # 遍历缺失的币对
        for item in progress_bar:
            if has_intervals:
                # 如果是带时间周期的元组
                pair_name, curr_interval = item
                progress_bar.set_description(f"处理 {pair_name} {curr_interval}")
            else:
                # 如果只是币对名称
                pair_name = item
                curr_interval = interval
                progress_bar.set_description(f"处理 {pair_name}")

            # 获取数据文件链接
            data_files = get_data_files(driver, pair_name, market_type, data_type, curr_interval, data_frequency)

            # 存储该币对的所有链接
            if data_files:
                # 根据数据类型和是否有时间周期决定如何存储
                if (data_type == "klines" or data_type.endswith("Klines")) and has_intervals:
                    # K线数据且有时间周期，使用嵌套字典
                    if pair_name not in existing_data:
                        existing_data[pair_name] = {}
                    
                    # 修复bug：不要覆盖原有数据，只添加新数据
                    if curr_interval not in existing_data[pair_name]:
                        existing_data[pair_name][curr_interval] = []
                    
                    # 检查重复链接，只添加新的
                    new_links = []
                    for link in data_files:
                        if link not in existing_data[pair_name][curr_interval]:
                            existing_data[pair_name][curr_interval].append(link)
                            new_links.append(link)
                    
                    if pair_name not in new_data:
                        new_data[pair_name] = {}
                    new_data[pair_name][curr_interval] = new_links

                    success_pairs.append((pair_name, curr_interval))
                    tqdm.write(f"成功获取 {pair_name} {curr_interval} 的 {len(new_links)} 个新数据文件")
                else:
                    # 非K线数据或无时间周期
                    # 修复bug：不要覆盖原有数据，只添加新数据
                    if pair_name not in existing_data:
                        existing_data[pair_name] = []
                    
                    # 检查重复链接，只添加新的
                    new_links = []
                    for link in data_files:
                        if link not in existing_data[pair_name]:
                            existing_data[pair_name].append(link)
                            new_links.append(link)
                    
                    new_data[pair_name] = new_links
                    success_pairs.append(pair_name if not has_intervals else (pair_name, curr_interval))
                    tqdm.write(f"成功获取 {pair_name} 的 {len(new_links)} 个新数据文件")
            else:
                failed_pairs.append(pair_name if not has_intervals else (pair_name, curr_interval))
                tqdm.write(f"未能获取 {pair_name} 的任何数据文件")

            # 每处理5个币对就先保存一次结果，避免数据丢失
            if len(success_pairs) % 5 == 0:
                # 在批量模式下减少日志输出
                if batch_mode:
                    with open(os.devnull, 'w') as f:
                        # 临时重定向标准输出
                        old_stdout = sys.stdout
                        sys.stdout = f
                        save_results(existing_data, data_type, market_type, data_frequency)
                        sys.stdout = old_stdout
                else:
                    save_results(existing_data, data_type, market_type, data_frequency)

        # 最终保存结果
        save_results(existing_data, data_type, market_type, data_frequency)

        # 最终统计
        logger.info("\n补充爬取完成")
        logger.info(f"成功获取数据的币对: {len(success_pairs)}/{len(missing_pairs)}")

        if success_pairs:
            logger.info("成功获取数据的币对列表:")
            for pair in success_pairs:
                if isinstance(pair, tuple):
                    p_name, p_interval = pair
                    if p_name in new_data and isinstance(new_data[p_name], dict) and p_interval in new_data[p_name]:
                        logger.info(f"  - {p_name} {p_interval} ({len(new_data[p_name][p_interval])} 个新文件)")
                    else:
                        logger.info(f"  - {p_name} {p_interval}")
                else:
                    logger.info(f"  - {pair} ({len(new_data[pair]) if pair in new_data else 0} 个新文件)")

        if failed_pairs:
            logger.warning("未能获取数据的币对列表:")
            for pair in failed_pairs:
                if isinstance(pair, tuple):
                    logger.warning(f"  - {pair[0]} {pair[1]}")
                else:
                    logger.warning(f"  - {pair}")

        # 如果我们创建了driver，则关闭它
        if close_driver:
            driver.quit()
            logger.info("浏览器已关闭")

        return new_data

    except Exception as e:
        logger.error(f"发生错误: {e}")
        # 确保浏览器关闭（如果我们创建了它）
        if close_driver:
            try:
                driver.quit()
            except:
                pass
        return {}

def extract_missing_pairs_from_log(log_file):
    """从日志文件中提取缺失的币对
    
    支持以下格式:
    1. 老格式的验证日志文件
    2. 新格式的验证日志文件，按市场和数据类型分类
    3. 新目录结构下的缺失币对文件和零数据币对文件
    
    Returns:
        tuple: (币对列表, 市场类型, 数据类型, 是否为零数据币对, 数据频率)
    """
    missing_pairs = []
    market_type = None
    data_type = None
    data_frequency = None
    is_zero_data = False
    
    try:
        # 判断文件路径，检测是否是新格式的单一数据类型文件
        if 'new_data' in log_file:
            # 检查是否是零数据币对文件
            if 'zero_data_pairs_' in log_file:
                is_zero_data = True
                logger.info("检测到零数据币对文件")
            
            # 从路径解析市场类型和数据类型
            parts = log_file.split(os.sep)
            if len(parts) >= 3 and parts[-3] == 'new_data':
                market_type = parts[-2].replace('_', '/')
                data_type = parts[-1]
                if '.' in data_type:  # 如果包含文件扩展名，去掉
                    data_type = os.path.splitext(data_type)[0]
                
                logger.info(f"从文件路径解析: 市场类型={market_type}, 数据类型={data_type}")
                
                # 根据数据类型判断可能的数据频率
                if data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
                    data_frequency = "daily"
                    logger.info(f"根据数据类型推断数据频率: {data_frequency}")
        
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
        
        # 尝试从文件内容确定市场类型、数据类型和数据频率
        if '市场类型:' in log_content:
            market_match = re.search(r'市场类型: ([^\n]+)\n', log_content)
            if market_match:
                market_type = market_match.group(1).strip()
                logger.info(f"从文件内容解析: 市场类型={market_type}")
        
        if '数据类型:' in log_content:
            data_type_match = re.search(r'数据类型: ([^\n]+)\n', log_content)
            if data_type_match:
                data_type = data_type_match.group(1).strip()
                logger.info(f"从文件内容解析: 数据类型={data_type}")
                
                # 根据数据类型判断可能的数据频率
                if data_frequency is None and data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
                    data_frequency = "daily"
                    logger.info(f"根据数据类型推断数据频率: {data_frequency}")
        
        # 尝试从文件内容解析数据频率
        if '数据频率:' in log_content:
            frequency_match = re.search(r'数据频率: ([^\n]+)\n', log_content)
            if frequency_match:
                data_frequency = frequency_match.group(1).strip()
                logger.info(f"从文件内容解析: 数据频率={data_frequency}")
        
        # 检查是否是零数据币对内容
        if not is_zero_data and ('零数据币对' in log_content or 'zero_data_pairs' in log_file):
            is_zero_data = True
            logger.info("从文件内容确定为零数据币对文件")
        
        # 尝试从文件名中提取数据频率
        if data_frequency is None:
            if "daily" in log_file:
                data_frequency = "daily"
                logger.info(f"从文件名提取数据频率: {data_frequency}")
            elif "monthly" in log_file:
                data_frequency = "monthly" 
                logger.info(f"从文件名提取数据频率: {data_frequency}")
        
        # 这不是验证日志文件，尝试直接解析内容
        logger.info(f"尝试直接从文件内容中提取币对: {log_file}")
        
        # 提取===开头的行作为币对标题，===之间的内容作为该币对的数据
        pair_sections = re.findall(r"=== ([^\n=]+) ===\s*\n([\s\S]*?)(?:\n===|\Z)", log_content)
        if pair_sections:
            for pair_name, section in pair_sections:
                excluded_headers = ["采集日期", "市场类型", "数据类型", "数据频率", "验证时间"]
                if not any(pair_name.startswith(header) for header in excluded_headers):
                    if is_valid_pair(pair_name):
                        missing_pairs.append(pair_name.strip())
                    else:
                        logger.warning(f"忽略无效币对格式: {pair_name}")
        else:
            # 如果没有找到===格式的部分，尝试直接按行解析
            for line in log_content.split('\n'):
                line = line.strip()
                if line and not line.startswith('===') and not line.startswith('---') and not line.startswith('#'):
                    # 检查是否包含空格（带时间周期的交易对）
                    if ' ' in line:
                        pair_parts = line.split(' ', 1)
                        if len(pair_parts) == 2:
                            pair_name, interval = pair_parts
                            if is_valid_interval(interval):
                                # 这是带时间周期的格式
                                if is_valid_pair(pair_name):
                                    missing_pairs.append((pair_name.strip(), interval.strip()))
                                else:
                                    logger.warning(f"忽略无效币对格式: {pair_name}")
                            else:
                                # 检查整行是否是有效币对格式
                                if is_valid_pair(line):
                                    missing_pairs.append(line)
                                else:
                                    logger.warning(f"忽略无效币对格式: {line}")
                        else:
                            if is_valid_pair(line):
                                missing_pairs.append(line)
                            else:
                                logger.warning(f"忽略无效币对格式: {line}")
                    else:
                        if is_valid_pair(line):
                            missing_pairs.append(line)
                        else:
                            logger.warning(f"忽略无效币对格式: {line}")
        
        # 去除重复项
        if missing_pairs:
            seen = set()
            unique_pairs = []
            for pair in missing_pairs:
                if pair not in seen:
                    seen.add(pair)
                    unique_pairs.append(pair)
            missing_pairs = unique_pairs
        
        # 如果解析出的币对包含元组，则认为是带时间周期的K线数据
        has_intervals = any(isinstance(p, tuple) for p in missing_pairs)
        
        logger.info(f"从文件中解析出 {len(missing_pairs)} 个{'零数据' if is_zero_data else '缺失的'}币对: {'带时间周期' if has_intervals else '无时间周期'}")
        if missing_pairs:
            if has_intervals:
                for i, (pair, interval) in enumerate(missing_pairs[:10]):
                    logger.info(f"  {i+1}. {pair} {interval}")
            else:
                for i, pair in enumerate(missing_pairs[:10]):
                    logger.info(f"  {i+1}. {pair}")
            
            if len(missing_pairs) > 10:
                logger.info(f"  ... 及更多 {len(missing_pairs)-10} 个币对")
        
        # 如果数据频率未被解析，则按以下规则设置默认值：
        if data_frequency is None:
            # 1. 对于特定数据类型，自动设为daily
            if data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
                data_frequency = "daily"
                logger.info(f"基于数据类型 {data_type} 自动设置数据频率为: daily")
            # 2. 其他情况默认使用monthly
            else:
                data_frequency = "monthly"
                logger.info(f"未能解析数据频率，使用默认值: {data_frequency}")
        
        return missing_pairs, market_type, data_type, is_zero_data, data_frequency
    
    except Exception as e:
        logger.error(f"从文件提取币对时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return [], None, None, False, "monthly"

def is_valid_pair(pair_str):
    """验证是否是有效的币对格式
    
    币安的币对通常满足以下特征:
    1. 由字母、数字组成，不含特殊字符(除了一些特定后缀如USDT、BUSD等)
    2. 长度至少3个字符
    3. 不能是数据类型、市场类型等标识符
    
    Returns:
        bool: 是否是有效的币对格式
    """
    if not pair_str or len(pair_str) < 3:
        return False
    
    # 过滤掉常见的错误标识符 - 只有当币对名称完全等于这些词时才排除
    invalid_identifiers = [
        "spot", "futures", "option", "um", "cm",
        "klines", "trades", "aggTrades", "bookTicker", "indexPriceKlines",
        "markPriceKlines", "premiumIndexKlines", "fundingRate", "bookDepth",
        "metrics", "liquidationSnapshot", "daily", "monthly",
        ":", "：", ",", "，", "=", "===", "---", ">", "<", "#"
    ]
    
    # 确保不是整个币对名称等于这些标识符（使用精确匹配）
    if pair_str.lower() in [id.lower() for id in invalid_identifiers]:
        return False
    
    # 检查是否包含非法字符（仅允许字母、数字和下划线）
    if not re.match(r'^[A-Za-z0-9_]+$', pair_str):
        return False
    
    # 扩展常见的币对后缀列表
    common_suffixes = [
        "USDT", "BUSD", "BTC", "ETH", "BNB", "USDC", "USD", 
        "TUSD", "DAI", "BIDR", "BRL", "AUD", "EUR", "GBP", 
        "RUB", "TRY", "ZAR", "UAH", "NGN", "VAI", "USDP", 
        "BKRW", "IDRT", "UP", "DOWN", "BEAR", "BULL",
        # 添加更多常见后缀
        "JPY", "KRW", "SGD", "INR", "PAX", "TRX", "XRP", "DOT"
    ]
    
    # 如果币对以常见后缀结尾，则视为有效
    for suffix in common_suffixes:
        if pair_str.endswith(suffix):
            return True
    
    # 额外检查：如果包含常见币种名称，也视为有效
    common_tokens = [
        "BTC", "ETH", "BNB", "XRP", "ADA", "DOT", "SOL", "DOGE", "SHIB", 
        "LINK", "UNI", "LTC", "BCH", "MATIC", "AVAX", "TRX", "ETC", 
        "ATOM", "XLM", "ALGO", "TRUMP", "MEME", "PEPE", "DOGE", "APE",
        # 添加更多常见币种名称
        "AVAX", "FIL", "NEAR", "ATOM", "CAKE", "APT", "OP", "ARB"
    ]
    
    # 使用完整的tokens检查，不做子字符串匹配
    for token in common_tokens:
        # 确保是完整token匹配，而不是子字符串匹配
        parts = re.split(r'([A-Z][a-z]*)', pair_str)
        parts = [p for p in parts if p]  # 移除空字符串
        if token in parts:
            return True
    
    # 如果名称长度大于5，可能是一个有效的币对名称
    if len(pair_str) > 5:
        return True
    
    # 默认情况：除非明显是无效的，否则认为是有效币对
    # 这样可以减少错误过滤掉有效币对的风险
    return True

def is_valid_interval(interval):
    """验证是否是有效的时间周期格式
    
    Args:
        interval: 时间周期字符串
    
    Returns:
        bool: 是否是有效的时间周期
    """
    valid_intervals = ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1mo"]
    
    # 检查是否是有效的时间周期格式
    if interval in valid_intervals:
        return True
    
    # 如果是纯数字，也有可能是有效的时间周期
    try:
        int(interval)
        return True
    except:
        return False

def save_new_data(new_data, market_type="futures/um", data_type="klines", data_frequency="monthly"):
    """保存新增数据到独立文件，按市场类型和数据类型分类
    
    Args:
        new_data: 新增数据的字典
        market_type: 市场类型
        data_type: 数据类型
        data_frequency: 数据频率
    
    Returns:
        生成的新数据文件路径
    """
    if not new_data:
        logger.info("没有新增数据，跳过保存")
        return None

    # 创建保存新增数据的目录结构
    new_data_dir = "new_data"
    if not os.path.exists(new_data_dir):
        os.makedirs(new_data_dir)
    
    # 创建市场类型目录
    market_str = market_type.replace("/", "_")
    market_dir = os.path.join(new_data_dir, market_str)
    if not os.path.exists(market_dir):
        os.makedirs(market_dir)
    
    # 创建数据类型目录
    data_type_dir = os.path.join(market_dir, data_type)
    if not os.path.exists(data_type_dir):
        os.makedirs(data_type_dir)
    
    # 生成文件名
    date_str = datetime.now().strftime("%Y%m%d")
    new_data_file = os.path.join(data_type_dir, f"new_data_{date_str}.txt")

    # 汇总链接数量
    total_links = 0
    pairs_with_data = 0
    link_stats = {}

    # 写入数据
    with open(new_data_file, "w") as f:
        f.write(f"===== 采集日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} =====\n")
        f.write(f"===== 市场类型: {market_type} =====\n")
        f.write(f"===== 数据类型: {data_type} =====\n")
        f.write(f"===== 数据频率: {data_frequency} =====\n\n")
        
        for pair_name, data in new_data.items():
            if data:  # 只处理有数据的币对
                pairs_with_data += 1
                
                if isinstance(data, dict):  # K线数据结构
                    f.write(f"=== {pair_name} ===\n")
                    pair_links = 0
                    
                    for interval, links in data.items():
                        if links:  # 只处理有链接的时间周期
                            f.write(f"--- {interval} ---\n")
                            for link in links:
                                f.write(f"{link}\n")
                                total_links += 1
                                pair_links += 1
                            f.write("\n")
                    
                    link_stats[pair_name] = pair_links
                else:  # 普通数据结构
                    f.write(f"=== {pair_name} ===\n")
                    for link in data:
                        f.write(f"{link}\n")
                        total_links += 1
                    f.write("\n")
                    
                    link_stats[pair_name] = len(data)

    # 创建汇总文件
    summary_file = os.path.join(data_type_dir, "summary.txt")
    
    # 读取现有汇总信息（如果存在）
    existing_summary = []
    if os.path.exists(summary_file):
        with open(summary_file, "r") as f:
            existing_summary = f.readlines()
    
    # 添加新的汇总信息
    with open(summary_file, "w") as f:
        # 首先写入现有的汇总信息
        if existing_summary:
            for line in existing_summary:
                f.write(line)
            f.write("\n")
        
        # 然后添加新的汇总条目
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {market_type}/{data_type} - {pairs_with_data}个币对, {total_links}个链接\n")
    
    logger.info(f"已保存新增数据到: {new_data_file}")
    logger.info(f"总共获取: {pairs_with_data}个币对, {total_links}个链接")
    
    return new_data_file

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="币安数据补充爬虫 - 补充爬取缺失的币对数据")
    
    # 文件名参数（包含缺失币对列表的文件）
    parser.add_argument("--file", "-f", type=str,
                        help="包含缺失币对列表的文件路径")
    
    # 市场类型参数（如果不指定，将尝试从文件名或内容中解析）
    parser.add_argument("--market", "-m", type=str,
                        choices=["spot", "futures/um", "futures/cm", "option"],
                        help="市场类型: spot(现货), futures/um(U本位合约), futures/cm(币本位合约), option(期权)")
    
    # 数据类型参数（如果不指定，将尝试从文件名或内容中解析）
    parser.add_argument("--data-type", "-d", type=str,
                        help="数据类型: klines, trades, aggTrades 等")
    
    # 时间周期参数（仅对K线数据有效）
    parser.add_argument("--interval", "-i", type=str, default="1d",
                        choices=["1m", "3m", "5m", "15m", "30m", 
                                 "1h", "2h", "4h", "6h", "8h", "12h", 
                                 "1d", "3d", "1w", "1mo"],
                        help="时间周期（仅对K线数据有效）")
    
    # 数据频率参数
    parser.add_argument("--frequency", type=str, default=None,
                        choices=["daily", "monthly"],
                        help="数据频率: daily(每日数据), monthly(月度数据)")
    
    # 批量模式参数
    parser.add_argument("--batch", "-b", action="store_true",
                        help="批量模式，减少日志输出")
    
    # 自动模式参数
    parser.add_argument("--auto", "-a", action="store_true",
                        help="自动模式，自动查找最新的验证结果并处理所有缺失币对")

    args = parser.parse_args()

    # 设置日志
    log_file = setup_logging(batch_mode=args.batch)
    logger.info(f"开始运行币安数据补充爬虫...")
    
    # 自动模式或未指定文件时的处理
    if args.auto or not args.file:
        logger.info("自动模式启动，搜索最新的验证结果...")
        
        # 查找最新的汇总报告
        newest_summary = find_newest_file("binance_links", "verification_summary_*.json")
        if not newest_summary:
            logger.error("未找到任何验证汇总报告，请先运行verify_completeness.py")
            return
        
        logger.info(f"找到最新的验证汇总报告: {newest_summary}")
        
        try:
            # 读取汇总报告
            with open(newest_summary, 'r', encoding='utf-8') as f:
                summary_data = json.load(f)
            
            # 处理所有缺失币对
            process_all_missing_pairs(summary_data, args)
            
        except Exception as e:
            logger.error(f"处理验证汇总报告时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
        return
    
    # 处理单个文件
    if not os.path.exists(args.file):
        logger.error(f"文件不存在: {args.file}")
        return
    
    logger.info(f"从文件中提取缺失的币对: {args.file}")
    
    # 从文件中提取缺失币对和相关信息
    missing_pairs, market_type, data_type, is_zero_data, extracted_frequency = extract_missing_pairs_from_log(args.file)
    
    # 确定市场类型、数据类型和数据频率，优先使用命令行参数，其次使用文件中提取的值
    market_type = args.market if args.market else market_type
    data_type = args.data_type if args.data_type else data_type
    
    # 数据频率处理逻辑改进：
    # 1. 优先使用命令行参数指定的频率
    # 2. 其次使用从文件内容提取的频率
    # 3. 最后根据数据类型自动设置频率
    if args.frequency:
        data_frequency = args.frequency
        logger.info(f"使用命令行参数指定的数据频率: {data_frequency}")
    elif extracted_frequency:
        data_frequency = extracted_frequency
        logger.info(f"使用从文件提取的数据频率: {data_frequency}")
    else:
        # 根据数据类型自动设置数据频率
        if data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
            data_frequency = "daily"
            logger.info(f"基于数据类型 {data_type} 自动设置数据频率为: daily")
        else:
            data_frequency = "monthly"
            logger.info(f"未指定数据频率，使用默认值: {data_frequency}")
    
    if not market_type:
        logger.error("无法确定市场类型，请使用--market参数指定")
        return
    
    if not data_type:
        logger.error("无法确定数据类型，请使用--data-type参数指定")
        return
    
    # 对于K线数据，检查是否有时间周期（如果文件中提取的是带周期的列表）
    has_intervals = any(isinstance(p, tuple) for p in missing_pairs)
    
    if data_type == "klines" or data_type.endswith("Klines"):
        if not has_intervals:
            logger.info(f"K线数据但未提取到时间周期，将使用默认时间周期: {args.interval}")
            # 转换为带时间周期的格式
            missing_pairs = [(pair, args.interval) for pair in missing_pairs]
    else:
        # 非K线数据，确保没有时间周期
        if has_intervals:
            logger.warning(f"非K线数据但提取到时间周期，将忽略时间周期信息")
            missing_pairs = [pair for pair, _ in missing_pairs]
    
    if not missing_pairs:
        logger.warning(f"未从文件中提取到任何缺失的币对: {args.file}")
        return
    
    logger.info(f"找到 {len(missing_pairs)} 个{'零数据' if is_zero_data else '缺失的'}币对，开始补充爬取")
    logger.info(f"市场类型: {market_type}, 数据类型: {data_type}, 数据频率: {data_frequency}")
    
    # 设置浏览器
    driver = setup_visible_browser()

    try:
        # 加载现有结果
        existing_data = load_existing_results(data_type, market_type, data_frequency)
        
        # 处理币对
        new_data = process_missing_pairs(
            missing_pairs, 
            driver=driver, 
            existing_data=existing_data,
            market_type=market_type,
            data_type=data_type,
            interval=args.interval,
            data_frequency=data_frequency,
            batch_mode=args.batch
        )
        
        # 保存新增数据
        if new_data:
            new_data_file = save_new_data(new_data, market_type, data_type, data_frequency)
            logger.info(f"补充爬取完成，新增数据已保存到: {new_data_file}")
            
            # 标记已处理的文件，避免重复处理
            processed_mark_file = args.file + ".processed"
            with open(processed_mark_file, "w") as f:
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"成功获取数据的币对数: {len(new_data)}\n")
            logger.info(f"已标记文件为已处理: {processed_mark_file}")
        else:
            logger.info("补充爬取完成，但没有获取到任何新数据")
    finally:
        # 关闭浏览器
        try:
            driver.quit()
            logger.info("浏览器已关闭")
        except:
            pass

def find_newest_file(directory, pattern):
    """查找目录中匹配模式的最新文件"""
    import glob
    
    # 获取所有匹配的文件
    files = glob.glob(os.path.join(directory, pattern))
    if not files:
        return None
    
    # 按修改时间排序，返回最新的文件
    return max(files, key=os.path.getmtime)

def process_all_missing_pairs(summary_data, args):
    """处理汇总报告中的所有缺失币对"""
    logger.info("开始处理所有缺失币对...")
    
    # 创建浏览器实例
    driver = setup_visible_browser()
    
    try:
        # 处理缺失的币对
        if "missing_pairs" in summary_data:
            for market_type, market_data in summary_data["missing_pairs"].items():
                for data_type, pairs in market_data.items():
                    if pairs:  # 只处理有缺失币对的数据类型
                        logger.info(f"处理 {market_type} - {data_type} 的缺失币对 ({len(pairs)}个)")
                        
                        # 数据频率处理逻辑：
                        # 1. 优先使用命令行参数指定的频率
                        # 2. 根据数据类型自动设置频率
                        data_frequency = args.frequency
                        if not args.frequency:
                            if data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
                                data_frequency = "daily"
                                logger.info(f"对于 {data_type} 自动使用 {data_frequency} 频率")
                            else:
                                data_frequency = "monthly"
                                logger.info(f"对于 {data_type} 自动使用 {data_frequency} 频率")
                        
                        # 创建临时文件
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        market_str = market_type.replace("/", "_")
                        temp_dir = os.path.join("new_data", market_str, data_type)
                        os.makedirs(temp_dir, exist_ok=True)
                        temp_file = os.path.join(temp_dir, f"temp_missing_pairs_{timestamp}.txt")
                        
                        # 写入币对列表
                        with open(temp_file, "w") as f:
                            f.write(f"===== {market_type} - {data_type} 缺失币对 =====\n")
                            f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write(f"市场类型: {market_type}\n")
                            f.write(f"数据类型: {data_type}\n")
                            f.write(f"数据频率: {data_frequency}\n\n")
                            for pair in sorted(pairs):
                                f.write(f"{pair}\n")
                        
                        # 加载现有结果
                        existing_data = load_existing_results(data_type, market_type, data_frequency)
                        
                        # 处理币对
                        new_data = process_missing_pairs(
                            pairs, 
                            driver=driver, 
                            existing_data=existing_data,
                            market_type=market_type,
                            data_type=data_type,
                            interval=args.interval,
                            data_frequency=data_frequency,
                            batch_mode=args.batch
                        )
                        
                        # 保存新增数据
                        if new_data:
                            new_data_file = save_new_data(new_data, market_type, data_type, data_frequency)
                            logger.info(f"补充爬取完成，新增数据已保存到: {new_data_file}")
                            
                            # 标记已处理的文件
                            with open(temp_file + ".processed", "w") as f:
                                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                                f.write(f"成功获取数据的币对数: {len(new_data)}\n")
                            
                            # 删除临时文件
                            try:
                                os.remove(temp_file)
                                logger.info(f"已删除临时文件: {temp_file}")
                            except:
                                pass
                        else:
                            logger.info(f"{market_type} - {data_type} 未获取到任何新数据")
        
        # 处理零数据币对
        if "zero_data_pairs" in summary_data:
            for market_type, market_data in summary_data["zero_data_pairs"].items():
                for data_type, pairs in market_data.items():
                    if pairs:  # 只处理有零数据币对的数据类型
                        logger.info(f"处理 {market_type} - {data_type} 的零数据币对 ({len(pairs)}个)")
                        
                        # 数据频率处理逻辑：
                        # 1. 优先使用命令行参数指定的频率
                        # 2. 根据数据类型自动设置频率
                        data_frequency = args.frequency
                        if not args.frequency:
                            if data_type in ["metrics", "liquidationSnapshot", "bookDepth"]:
                                data_frequency = "daily"
                                logger.info(f"对于 {data_type} 自动使用 {data_frequency} 频率")
                            else:
                                data_frequency = "monthly"
                                logger.info(f"对于 {data_type} 自动使用 {data_frequency} 频率")
                        
                        # 创建临时文件
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        market_str = market_type.replace("/", "_")
                        temp_dir = os.path.join("new_data", market_str, data_type)
                        os.makedirs(temp_dir, exist_ok=True)
                        temp_file = os.path.join(temp_dir, f"temp_zero_data_pairs_{timestamp}.txt")
                        
                        # 写入币对列表
                        with open(temp_file, "w") as f:
                            f.write(f"===== {market_type} - {data_type} 零数据币对 =====\n")
                            f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write(f"市场类型: {market_type}\n")
                            f.write(f"数据类型: {data_type}\n")
                            f.write(f"数据频率: {data_frequency}\n\n")
                            for pair in sorted(pairs):
                                f.write(f"{pair}\n")
                        
                        # 加载现有结果
                        existing_data = load_existing_results(data_type, market_type, data_frequency)
                        
                        # 处理币对
                        new_data = process_missing_pairs(
                            pairs, 
                            driver=driver, 
                            existing_data=existing_data,
                            market_type=market_type,
                            data_type=data_type,
                            interval=args.interval,
                            data_frequency=data_frequency,
                            batch_mode=args.batch
                        )
                        
                        # 保存新增数据
                        if new_data:
                            new_data_file = save_new_data(new_data, market_type, data_type, data_frequency)
                            logger.info(f"补充爬取完成，新增数据已保存到: {new_data_file}")
                            
                            # 标记已处理的文件
                            with open(temp_file + ".processed", "w") as f:
                                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                                f.write(f"成功获取数据的币对数: {len(new_data)}\n")
                            
                            # 删除临时文件
                            try:
                                os.remove(temp_file)
                                logger.info(f"已删除临时文件: {temp_file}")
                            except:
                                pass
                        else:
                            logger.info(f"{market_type} - {data_type} 未获取到任何新数据")
        
        logger.info("所有缺失币对和零数据币对处理完成")
        
    finally:
        # 关闭浏览器
        try:
            driver.quit()
            logger.info("浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    main()