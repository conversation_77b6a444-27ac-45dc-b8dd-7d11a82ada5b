def process_missing_pairs_with_intervals(missing_pairs, driver, all_links, market_type, data_type):
    """处理带有时间周期的缺失币对
    
    Args:
        missing_pairs: 缺失的币对和时间周期列表，格式为[(pair_name, interval), ...]
        driver: WebDriver实例
        all_links: 所有链接的字典
        market_type: 市场类型
        data_type: 数据类型
        
    Returns:
        补充获取的数据字典，格式为{pair_name: {interval: [links]}}
    """
    logger.info(f"开始处理 {len(missing_pairs)} 个缺失的币对和时间周期...")
    
    supplementary_data = {}
    
    for i, (pair_name, interval) in enumerate(missing_pairs):
        logger.info(f"补充爬取 {i+1}/{len(missing_pairs)}: {pair_name} {interval}")
        
        # 构建URL
        pair_url = f"{BASE_URL}?prefix=data/{market_type}/monthly/{data_type}/{pair_name}/"
        
        # 获取数据文件
        data_files = get_data_files_with_visible_browser(driver, pair_name, pair_url, interval)
        
        if data_files:
            # 更新all_links
            if pair_name not in all_links:
                all_links[pair_name] = {}
            all_links[pair_name][interval] = data_files
            
            # 添加到补充数据
            if pair_name not in supplementary_data:
                supplementary_data[pair_name] = {}
            supplementary_data[pair_name][interval] = data_files
            
            logger.info(f"成功补充获取 {pair_name} {interval} 的 {len(data_files)} 个数据文件")
        else:
            logger.warning(f"无法获取 {pair_name} {interval} 的数据文件")
    
    return supplementary_data
