#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
币安数据链接提取与分割工具

这个脚本用于从指定目录下的txt文件中提取所有链接，
并将它们按每9999个链接分割成多个文件，方便导入下载工具。

使用方法:
1. 修改下方的配置部分
2. 运行脚本: python split_links.py
"""

import os
import re
from datetime import datetime
import logging

#====================== 配置部分 - 请在这里修改参数 ======================

# 输入目录 - 可以是以下选项之一:
# 1. "binance_links" - 处理binance_links目录下的所有txt文件
# 2. "new_data" - 处理new_data目录下的所有txt文件
# 3. 其他任意目录路径，例如 "H:/binance_crawler/binance_links"
INPUT_DIR = "binance_links/分割文件夹"

# 输出目录 - 链接文件将保存在这个目录下
OUTPUT_DIR = "download_links"

# 每个文件的最大链接数
MAX_LINKS_PER_FILE = 9999

# 是否按市场类型分类 (True/False)
# - True: 将链接按市场类型(spot, futures_um, futures_cm)分类保存到不同子目录
# - False: 所有链接保存在同一目录下
CLASSIFY_BY_MARKET = False

# 是否包含子目录 (True/False)
# - True: 递归处理输入目录下的所有子目录中的txt文件
# - False: 只处理输入目录下的txt文件，不包括子目录
INCLUDE_SUBDIRS = False

#====================== 配置结束 ======================

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 市场类型
MARKET_SPOT = "spot"
MARKET_FUTURES_UM = "futures/um"
MARKET_FUTURES_CM = "futures/cm"

def extract_links_from_file(file_path):
    """从文件中提取所有链接"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()

            # 使用正则表达式提取所有链接
            links = re.findall(r'https?://[^\s\n]+\.zip', content)
            logger.info(f"从 {file_path} 中提取了 {len(links)} 个链接 (使用编码: {encoding})")
            return links
        except UnicodeDecodeError:
            # 如果当前编码无法解码，尝试下一个编码
            continue
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return []

    # 如果所有编码都失败了，尝试二进制模式读取
    try:
        logger.warning(f"尝试使用二进制模式读取文件 {file_path}")
        with open(file_path, 'rb') as f:
            content = f.read().decode('utf-8', errors='ignore')

        # 使用正则表达式提取所有链接
        links = re.findall(r'https?://[^\s\n]+\.zip', content)
        logger.info(f"从 {file_path} 中提取了 {len(links)} 个链接 (使用二进制模式)")
        return links
    except Exception as e:
        logger.error(f"无法使用任何方法读取文件 {file_path}: {e}")
        return []

def classify_links_by_market(links):
    """按市场类型分类链接"""
    classified_links = {
        MARKET_SPOT: [],
        MARKET_FUTURES_UM: [],
        MARKET_FUTURES_CM: [],
        "other": []
    }

    for link in links:
        if f"/data/{MARKET_SPOT}/" in link:
            classified_links[MARKET_SPOT].append(link)
        elif f"/data/{MARKET_FUTURES_UM}/" in link:
            classified_links[MARKET_FUTURES_UM].append(link)
        elif f"/data/{MARKET_FUTURES_CM}/" in link:
            classified_links[MARKET_FUTURES_CM].append(link)
        else:
            classified_links["other"].append(link)

    return classified_links

def save_links_to_files(links, output_dir, prefix="links", limit=9999):
    """将链接保存到多个文件中，每个文件最多包含limit个链接"""
    if not links:
        logger.info(f"没有链接需要保存，跳过")
        return 0

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 计算需要创建的文件数量
    num_files = (len(links) + limit - 1) // limit

    # 生成时间戳，用于文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 分割链接并保存到多个文件
    for i in range(num_files):
        start_idx = i * limit
        end_idx = min((i + 1) * limit, len(links))
        current_links = links[start_idx:end_idx]

        # 生成文件名
        file_name = f"{prefix}_part{i+1:03d}_{timestamp}.txt"
        file_path = os.path.join(output_dir, file_name)

        # 保存链接到文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(current_links))
        except Exception as e:
            logger.error(f"保存文件 {file_path} 时出错: {e}")
            # 尝试使用其他编码
            try:
                with open(file_path, 'w', encoding='gbk') as f:
                    f.write('\n'.join(current_links))
            except Exception as e2:
                logger.error(f"使用备用编码保存文件 {file_path} 时也出错: {e2}")

        logger.info(f"保存了 {len(current_links)} 个链接到文件 {file_path}")

    return num_files

def save_links_by_market(links, output_dir, limit=9999):
    """按市场类型分类保存链接"""
    # 按市场类型分类链接
    classified_links = classify_links_by_market(links)

    # 统计各市场类型的链接数量
    for market_type, market_links in classified_links.items():
        logger.info(f"市场 {market_type} 有 {len(market_links)} 个链接")

    # 保存各市场类型的链接到多个文件
    total_files = 0
    for market_type, market_links in classified_links.items():
        if not market_links:
            continue

        # 创建市场类型子目录
        market_dir = os.path.join(output_dir, market_type.replace("/", "_"))
        if not os.path.exists(market_dir):
            os.makedirs(market_dir)

        # 保存链接
        prefix = market_type.replace("/", "_")
        num_files = save_links_to_files(market_links, market_dir, prefix, limit)
        total_files += num_files

    return total_files

def find_txt_files(input_dir, include_subdirs=False):
    """查找指定目录下的所有txt文件"""
    txt_files = []

    if include_subdirs:
        # 递归查找所有子目录
        for root, dirs, files in os.walk(input_dir):
            for file in files:
                if file.endswith('.txt'):
                    txt_files.append(os.path.join(root, file))
    else:
        # 只查找当前目录
        for file in os.listdir(input_dir):
            if file.endswith('.txt'):
                txt_files.append(os.path.join(input_dir, file))

    return txt_files

def process_txt_files(txt_files, output_dir, by_market=False, limit=9999):
    """处理指定的txt文件列表"""
    all_links = []

    if not txt_files:
        logger.warning("没有找到任何txt文件")
        return 0

    logger.info(f"找到 {len(txt_files)} 个txt文件")

    # 从每个文件中提取链接
    for file_path in txt_files:
        links = extract_links_from_file(file_path)
        all_links.extend(links)

    # 去除重复链接
    unique_links = list(set(all_links))
    logger.info(f"总共提取了 {len(all_links)} 个链接，去重后剩余 {len(unique_links)} 个链接")

    # 保存链接
    if by_market:
        # 按市场类型分类保存
        num_files = save_links_by_market(unique_links, output_dir, limit)
    else:
        # 不分类，直接保存
        prefix = "binance_links"
        num_files = save_links_to_files(unique_links, output_dir, prefix, limit)

    return num_files

def main():
    """主函数"""
    try:
        logger.info("币安数据链接提取与分割工具启动")
        logger.info(f"输入目录: {INPUT_DIR}")
        logger.info(f"输出目录: {OUTPUT_DIR}")
        logger.info(f"每个文件最大链接数: {MAX_LINKS_PER_FILE}")
        logger.info(f"是否按市场类型分类: {CLASSIFY_BY_MARKET}")
        logger.info(f"是否包含子目录: {INCLUDE_SUBDIRS}")

        # 检查输入目录是否存在
        if not os.path.exists(INPUT_DIR):
            logger.error(f"输入目录 {INPUT_DIR} 不存在")
            return

        # 创建输出目录
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)

        # 查找所有txt文件
        txt_files = find_txt_files(INPUT_DIR, INCLUDE_SUBDIRS)

        # 处理所有txt文件
        num_files = process_txt_files(txt_files, OUTPUT_DIR, CLASSIFY_BY_MARKET, MAX_LINKS_PER_FILE)

        if num_files > 0:
            logger.info(f"处理完成，生成了 {num_files} 个文件，保存在 {OUTPUT_DIR} 目录下")
        else:
            logger.warning("没有生成任何文件，请检查输入目录是否包含有效的txt文件")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
