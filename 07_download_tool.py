import os
import re
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time

# 配置参数
CONFIG = {
    "links_dir": "H:\\binance_crawler\\download_links",  # 链接文件目录
    "output_dir": "H:\\binance_crawler\\downloads",     # 下载文件保存目录
    "max_workers": 50,                              # 并发线程数
    "max_retries": 3,                               # 单个文件重试次数
    "timeout": 10,                                  # 单个请求超时时间（秒）
    "url_pattern": r'^https?://[^\s/$.?#].[^\s]*$',   # URL正则表达式
    "error_log": "H:\\binance_crawler\\invalid_urls.log",  # 无效内容日志
    "download_errors_log": "H:\\binance_crawler\\download_errors.log",  # 下载失败日志
    "resume_mode": True,                           # 是否为断点续传模式，
    "headers": {  # 请求头设置
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
}


def get_filename_from_url(url):
    """从URL提取文件名"""
    filename = os.path.basename(url.split('?')[0])
    if not filename:
        filename = f"file_{int(time.time())}.dat"
    return filename

def is_file_downloaded(url):
    """检查文件是否已经下载"""
    filename = get_filename_from_url(url)
    save_path = os.path.join(CONFIG['output_dir'], filename)
    return os.path.exists(save_path) and os.path.getsize(save_path) > 0

def read_failed_urls():
    """从失败日志中读取失败的URL"""
    failed_urls = []
    if os.path.exists(CONFIG['download_errors_log']):
        try:
            with open(CONFIG['download_errors_log'], 'r', encoding='utf-8') as f:
                for line in f:
                    # 提取URL（假设格式为"URL\t错误信息"）
                    parts = line.strip().split('\t')
                    if parts and is_valid_url(parts[0]):
                        failed_urls.append(parts[0])
            print(f"从失败日志中读取了 {len(failed_urls)} 个失败URL")
        except Exception as e:
            print(f"读取失败日志时出错: {str(e)}")
    return failed_urls

def download_file(url, session):
    """下载单个文件并保存到本地"""
    # 如果在断点续传模式下文件已存在，直接返回成功
    if CONFIG['resume_mode'] and is_file_downloaded(url):
        return url, True

    for attempt in range(CONFIG['max_retries']):
        try:
            # 从URL提取文件名
            filename = get_filename_from_url(url)
            save_path = os.path.join(CONFIG['output_dir'], filename)

            # 发送请求
            with session.get(url, stream=True, timeout=CONFIG['timeout'], headers=CONFIG['headers']) as r:
                r.raise_for_status()

                # 保存文件
                with open(save_path, 'wb') as f:
                    for chunk in r.iter_content(chunk_size=8192):
                        f.write(chunk)
            return url, True
        except Exception as e:
            if attempt == CONFIG['max_retries'] - 1:
                return url, f"Failed after {CONFIG['max_retries']} retries: {str(e)}"
            time.sleep(1)  # 失败后等待1秒重试


def is_valid_url(url):
    """用正则验证是否为有效URL"""
    return re.match(CONFIG['url_pattern'], url) is not None

def get_txt_files(directory):
    """获取指定目录下的所有txt文件"""
    txt_files = []
    try:
        # 确保目录存在
        if not os.path.exists(directory):
            print(f"目录 {directory} 不存在，将创建该目录")
            os.makedirs(directory, exist_ok=True)
            return txt_files

        # 获取所有txt文件
        for file in os.listdir(directory):
            if file.endswith('.txt'):
                txt_files.append(os.path.join(directory, file))

        print(f"在 {directory} 目录下找到 {len(txt_files)} 个txt文件")
    except Exception as e:
        print(f"读取目录时出错: {str(e)}")

    return txt_files

def filter_urls():
    """从所有txt文件中过滤并返回有效URL列表，同时记录无效内容"""
    valid_urls = []
    invalid_lines = []

    # 获取所有txt文件
    txt_files = get_txt_files(CONFIG['links_dir'])
    if not txt_files:
        print(f"在 {CONFIG['links_dir']} 目录下没有找到txt文件")
        return valid_urls

    # 从每个文件中读取链接
    for txt_file in txt_files:
        try:
            # 尝试不同编码打开文件
            content = None
            encodings = ['utf-8', 'gbk', 'latin1']

            for encoding in encodings:
                try:
                    with open(txt_file, 'r', encoding=encoding) as f:
                        content = f.readlines()
                    break  # 成功读取，跳出循环
                except UnicodeDecodeError:
                    continue  # 尝试下一种编码

            # 如果所有编码都失败，尝试二进制模式
            if content is None:
                with open(txt_file, 'rb') as f:
                    content = f.read().decode('utf-8', errors='ignore').splitlines()

            # 处理每一行
            file_name = os.path.basename(txt_file)
            for line_num, line in enumerate(content, 1):
                line = line.strip()
                if not line:
                    continue  # 跳过空行
                if is_valid_url(line):
                    valid_urls.append(line)
                else:
                    invalid_lines.append(f"{file_name}:{line_num}: {line}")

        except Exception as e:
            print(f"处理文件 {txt_file} 时出错: {str(e)}")

    # 去除重复链接
    valid_urls = list(set(valid_urls))
    print(f"总共提取了 {len(valid_urls)} 个有效链接")

    # 记录无效内容
    if invalid_lines:
        with open(CONFIG['error_log'], 'w', encoding='utf-8') as f:
            f.write("以下内容被识别为无效URL:\n" + "\n".join(invalid_lines))
        print(f"发现 {len(invalid_lines)} 条无效内容，已保存至 {CONFIG['error_log']}")

    return valid_urls

def download_urls(urls, desc="下载进度"):
    """下载指定的URL列表"""
    if not urls:
        print("没有URL需要下载")
        return []

    # 创建下载目录
    os.makedirs(CONFIG['output_dir'], exist_ok=True)

    error_log = []

    # 使用Session保持连接池
    with requests.Session() as session:
        # 创建线程池
        with ThreadPoolExecutor(max_workers=CONFIG['max_workers']) as executor:
            # 提交任务
            futures = {executor.submit(download_file, url, session): url for url in urls}

            # 进度条监控
            with tqdm(total=len(urls), desc=desc) as pbar:
                success_count = 0

                for future in as_completed(futures):
                    url, result = future.result()
                    if result is True:
                        success_count += 1
                    else:
                        error_log.append(f"{url}\t{result}")
                    pbar.update(1)
                    pbar.set_postfix_str(f"成功: {success_count} 失败: {len(error_log)}")

    return error_log

def batch_download():
    """批量下载链接文件中的URL"""
    # 新增：过滤URL
    urls = filter_urls()
    if not urls:
        print("没有有效URL可下载！")
        return

    # 如果在断点续传模式下，过滤掉已下载的文件
    if CONFIG['resume_mode']:
        original_count = len(urls)
        urls = [url for url in urls if not is_file_downloaded(url)]
        skipped_count = original_count - len(urls)
        if skipped_count > 0:
            print(f"断点续传模式: 跳过了 {skipped_count} 个已下载的文件")

    # 下载文件
    error_log = download_urls(urls)

    # 保存错误日志
    if error_log:
        with open(CONFIG['download_errors_log'], "w", encoding='utf-8') as f:
            f.write("\n".join(error_log))
        print(f"\n{len(error_log)}个文件下载失败，详见 {CONFIG['download_errors_log']}")
        print("可以使用断点续传模式重新下载失败的文件：")
        print("python download_tool.py --resume")

def resume_download():
    """断点续传模式，下载失败的文件"""
    # 设置为断点续传模式
    CONFIG['resume_mode'] = True

    # 从失败日志中读取失败的URL
    failed_urls = read_failed_urls()

    if not failed_urls:
        print("没有找到失败的URL或失败日志不存在")
        return

    # 过滤掉已下载的文件
    original_count = len(failed_urls)
    failed_urls = [url for url in failed_urls if not is_file_downloaded(url)]
    skipped_count = original_count - len(failed_urls)

    if skipped_count > 0:
        print(f"跳过了 {skipped_count} 个已下载的文件")

    if not failed_urls:
        print("所有失败的文件已经下载完成")
        return

    # 下载失败的文件
    print(f"开始重新下载 {len(failed_urls)} 个失败的文件...")
    error_log = download_urls(failed_urls, desc="断点续传")

    # 更新错误日志
    if error_log:
        with open(CONFIG['download_errors_log'], "w", encoding='utf-8') as f:
            f.write("\n".join(error_log))
        print(f"\n仍有 {len(error_log)} 个文件下载失败，详见 {CONFIG['download_errors_log']}")
    else:
        # 如果所有文件都下载成功，删除错误日志
        if os.path.exists(CONFIG['download_errors_log']):
            os.remove(CONFIG['download_errors_log'])
        print("所有失败的文件已成功下载")


def parse_args():
    """解析命令行参数"""
    import argparse
    parser = argparse.ArgumentParser(description="币安数据下载工具")
    parser.add_argument("--resume", action="store_true", help="断点续传模式，重新下载失败的文件")
    parser.add_argument("--workers", type=int, help="并发线程数，默认为50")
    parser.add_argument("--timeout", type=int, help="请求超时时间（秒），默认为10")
    parser.add_argument("--retries", type=int, help="重试次数，默认为3")
    parser.add_argument("--links-dir", help="链接文件目录，默认为 H:\\binance_crawler\\download_links")
    parser.add_argument("--output-dir", help="输出目录，默认为 H:\\binance_crawler\\downloads")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()

    # 更新配置
    if args.workers:
        CONFIG['max_workers'] = args.workers
    if args.timeout:
        CONFIG['timeout'] = args.timeout
    if args.retries:
        CONFIG['max_retries'] = args.retries
    if args.links_dir:
        CONFIG['links_dir'] = args.links_dir
    if args.output_dir:
        CONFIG['output_dir'] = args.output_dir

    # 断点续传模式
    if args.resume:
        CONFIG['resume_mode'] = True
        resume_download()
    else:
        batch_download()