#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
币安数据下载检查工具

这个脚本用于检查下载文件是否有缺失，并将缺失的链接汇总到一个表格中。
它会比较原始链接文件和已下载的文件，找出哪些链接对应的文件尚未下载。

使用方法:
1. 修改下方的配置部分
2. 运行脚本: python check_downloads.py
"""

import os
import re
import csv
from datetime import datetime
import logging
from urllib.parse import urlparse, unquote

#====================== 配置部分 - 请在这里修改参数 ======================

# 链接文件目录 - 包含原始链接的txt文件所在的目录
LINKS_DIR = "binance_links"

# 下载文件目录 - 已下载的zip文件所在的目录
DOWNLOAD_DIR = "Z:\\Binance"

# 输出文件 - 缺失链接的CSV文件路径
OUTPUT_FILE = "missing_links.csv"

# 是否包含子目录 (True/False)
# - True: 递归检查下载目录下的所有子目录中的文件
# - False: 只检查下载目录下的文件，不包括子目录
INCLUDE_SUBDIRS = True

# 是否只检查特定市场类型的链接 (None或字符串)
# - None: 检查所有市场类型的链接
# - "spot": 只检查现货市场的链接
# - "futures_um": 只检查U本位合约市场的链接
# - "futures_cm": 只检查币本位合约市场的链接
MARKET_FILTER = None

#====================== 配置结束 ======================

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 市场类型
MARKET_SPOT = "spot"
MARKET_FUTURES_UM = "futures/um"
MARKET_FUTURES_CM = "futures/cm"

def extract_links_from_file(file_path):
    """从文件中提取所有链接"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()

            # 使用正则表达式提取所有链接
            links = re.findall(r'https?://[^\s\n]+\.zip', content)
            logger.info(f"从 {file_path} 中提取了 {len(links)} 个链接")
            return links
        except UnicodeDecodeError:
            # 尝试下一种编码
            continue
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return []

    # 如果所有编码都失败，尝试二进制模式读取
    try:
        with open(file_path, 'rb') as f:
            content = f.read().decode('utf-8', errors='ignore')

        links = re.findall(r'https?://[^\s\n]+\.zip', content)
        logger.info(f"从 {file_path} 中提取了 {len(links)} 个链接 (使用二进制模式)")
        return links
    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错 (二进制模式): {e}")
        return []

def find_txt_files(input_dir, include_subdirs=False):
    """查找指定目录下的所有txt文件"""
    txt_files = []

    if include_subdirs:
        # 递归查找所有子目录
        for root, dirs, files in os.walk(input_dir):
            for file in files:
                if file.endswith('.txt'):
                    txt_files.append(os.path.join(root, file))
    else:
        # 只查找当前目录
        for file in os.listdir(input_dir):
            if file.endswith('.txt'):
                txt_files.append(os.path.join(input_dir, file))

    return txt_files

def find_zip_files(download_dir, include_subdirs=False):
    """查找指定目录下的所有zip文件"""
    zip_files = []

    if include_subdirs:
        # 递归查找所有子目录
        for root, dirs, files in os.walk(download_dir):
            for file in files:
                if file.endswith('.zip'):
                    zip_files.append(os.path.join(root, file))
    else:
        # 只查找当前目录
        for file in os.listdir(download_dir):
            if file.endswith('.zip'):
                zip_files.append(os.path.join(download_dir, file))

    return zip_files

def get_filename_from_url(url):
    """从URL中提取文件名"""
    parsed_url = urlparse(url)
    path = unquote(parsed_url.path)
    return os.path.basename(path)

def get_market_from_url(url):
    """从URL中提取市场类型"""
    # 处理标准币安数据市场 URL
    if f"/data/{MARKET_SPOT}/" in url:
        return MARKET_SPOT.replace("/", "_")
    elif f"/data/{MARKET_FUTURES_UM}/" in url:
        return MARKET_FUTURES_UM.replace("/", "_")
    elif f"/data/{MARKET_FUTURES_CM}/" in url:
        return MARKET_FUTURES_CM.replace("/", "_")

    # 处理可能的其他格式
    if "/spot/" in url or "spot-" in url:
        return "spot"
    elif "/futures-um/" in url or "/futures_um/" in url or "futures-um-" in url:
        return "futures_um"
    elif "/futures-cm/" in url or "/futures_cm/" in url or "futures-cm-" in url:
        return "futures_cm"
    else:
        return "other"

def get_interval_from_url(url):
    """从URL中提取时间周期"""
    # 定义所有可能的时间周期
    intervals = [
        "1s", "1m", "3m", "5m", "15m", "30m",
        "1h", "2h", "4h", "6h", "8h", "12h",
        "1d", "3d", "1w", "1mo", "1min"
    ]

    # 从 URL 中提取时间周期
    for interval in intervals:
        # 检查标准格式
        if f"/{interval}/" in url:
            return interval
        # 检查文件名中的时间周期
        elif f"-{interval}-" in url or f"-{interval}." in url:
            return interval
        # 检查文件夹名称中的时间周期
        elif f"-{interval}" in url.split("/")[-2]:
            return interval

    # 特殊处理某些常见的替代形式
    if "-1min" in url:
        return "1m"

    return None

def get_data_type_from_url(url):
    """从URL中提取数据类型"""
    # 定义所有可能的数据类型
    data_types = [
        "klines",
        "trades",
        "aggTrades",
        "bookTicker",
        "indexPriceKlines",
        "markPriceKlines",
        "premiumIndexKlines",
        "fundingRate",
        "bookDepth",
        "metrics",
        "liquidationSnapshot"
    ]

    # 从 URL 中提取数据类型
    for data_type in data_types:
        # 检查标准格式
        if f"/{data_type}/" in url:
            return data_type
        # 检查带短横线的格式，如 futures-um-monthly-klines-1d
        elif f"-{data_type}-" in url:
            return data_type
        # 检查其他可能的格式
        elif f"-{data_type}" in url.lower():
            return data_type

    # 特殊处理
    if "资金费率" in url:
        return "fundingRate"

    return "unknown"

def filter_links_by_market(links, market_filter):
    """按市场类型过滤链接"""
    if not market_filter:
        return links

    filtered_links = []
    market_filter = market_filter.replace("_", "/")

    for link in links:
        if f"/data/{market_filter}/" in link:
            filtered_links.append(link)

    logger.info(f"按市场类型 {market_filter} 过滤后剩余 {len(filtered_links)} 个链接")
    return filtered_links

def check_downloads(links, download_dir, include_subdirs=False):
    """检查哪些链接对应的文件尚未下载"""
    # 按数据类型和市场类型分组链接
    links_by_type = {}

    for link in links:
        market_type = get_market_from_url(link)
        data_type = get_data_type_from_url(link)
        interval = get_interval_from_url(link)

        # 如果是klines类型的数据但没有时间周期，默认为1d
        if data_type in ["klines", "indexPriceKlines", "markPriceKlines", "premiumIndexKlines"] and not interval:
            interval = "1d"  # 默认时间周期

        # 对于klines类型的数据，将时间周期也加入key
        if data_type in ["klines", "indexPriceKlines", "markPriceKlines", "premiumIndexKlines"] and interval:
            key = f"{market_type}_{data_type}_{interval}"
        else:
            key = f"{market_type}_{data_type}"

        if key not in links_by_type:
            links_by_type[key] = []

        links_by_type[key].append(link)

    # 查找所有已下载的zip文件
    zip_files = find_zip_files(download_dir, include_subdirs)
    logger.info(f"在下载目录中找到 {len(zip_files)} 个zip文件")

    # 按数据类型和市场类型分组已下载的文件
    downloaded_files_by_type = {}

    for zip_file in zip_files:
        # 尝试从文件路径中提取市场类型和数据类型
        file_path = os.path.normpath(zip_file).replace("\\", "/")

        # 默认分类
        market_type = "other"
        data_type = "unknown"
        interval = None

        # 检查市场类型 - 适应Z:\Binance文件夹结构
        if "spot-" in file_path:
            market_type = "spot"
        elif "futures-um-" in file_path:
            market_type = "futures_um"
        elif "futures-cm-" in file_path:
            market_type = "futures_cm"

        # 检查数据类型 - 适应Z:\Binance文件夹结构
        data_types_map = {
            "klines": "klines",
            "trades": "trades",
            "aggTrades": "aggTrades",
            "bookTicker": "bookTicker",
            "indexPriceKlines": "indexPriceKlines",
            "markPriceKlines": "markPriceKlines",
            "premiumIndexKlines": "premiumIndexKlines",
            "fundingRate": "fundingRate",
            "bookDepth": "bookDepth",
            "metrics": "metrics",
            "liquidationSnapshot": "liquidationSnapshot"
        }

        # 从文件路径中提取数据类型
        # 处理如 Z:\Binance\futures-um-monthly-klines-1d 这样的路径
        folder_name = os.path.basename(os.path.dirname(file_path))
        if not folder_name or folder_name == "Binance":
            # 如果文件直接在Binance文件夹下，则使用文件所在的目录名称
            folder_name = os.path.basename(os.path.dirname(file_path))

        # 如果路径中包含数据类型关键字，直接提取
        for dt in data_types_map.keys():
            if dt in file_path:
                data_type = dt
                break

        # 从文件路径中提取时间周期
        # 先检查文件名中的时间周期
        filename = os.path.basename(zip_file)
        for intvl in ["1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1mo", "1min"]:
            if f"-{intvl}-" in filename or f"-{intvl}." in filename:
                interval = intvl
                break

        # 如果文件名中没有时间周期，则检查文件夹名称
        if not interval and folder_name != "Binance":
            # 处理带时间周期的文件夹名称
            folder_parts = folder_name.split('-')
            if len(folder_parts) >= 4:  # 假设格式为 market-frequency-datatype-interval
                interval_part = folder_parts[-1]  # 最后一个部分可能是时间周期
                if interval_part in ["1d", "1min"]:
                    interval = interval_part
                    if interval == "1min":  # 标准化时间周期
                        interval = "1m"

        # 如果上面的方法没有找到数据类型，则尝试从文件夹名称中提取
        if data_type == "unknown" and folder_name != "Binance":
            # 处理带时间周期的文件夹名称
            folder_parts = folder_name.split('-')
            if len(folder_parts) >= 3:
                # 假设格式为 market-frequency-datatype-interval
                # 例如 futures-um-monthly-klines-1d
                data_type_idx = 3  # 默认数据类型在第4个位置

                if len(folder_parts) > data_type_idx:
                    data_type_part = folder_parts[data_type_idx]

                    # 处理可能的时间周期后缀
                    if '-' in data_type_part:
                        data_type_part = data_type_part.split('-')[0]

                    # 将数据类型映射到标准名称
                    for dt, std_dt in data_types_map.items():
                        if dt in data_type_part:
                            data_type = std_dt
                            break

        # 特殊处理某些文件夹
        if "合约-资金费率" in file_path:
            data_type = "fundingRate"

        # 如果是klines类型的数据但没有时间周期，默认为1d
        if data_type in ["klines", "indexPriceKlines", "markPriceKlines", "premiumIndexKlines"] and not interval:
            interval = "1d"  # 默认时间周期

        # 如果无法从路径中提取，则使用“其他”分类
        if data_type in ["klines", "indexPriceKlines", "markPriceKlines", "premiumIndexKlines"] and interval:
            key = f"{market_type}_{data_type}_{interval}"
        else:
            key = f"{market_type}_{data_type}"

        if key not in downloaded_files_by_type:
            downloaded_files_by_type[key] = set()

        filename = os.path.basename(zip_file)
        downloaded_files_by_type[key].add(filename)

    # 检查哪些链接对应的文件尚未下载
    missing_links = []

    for key, type_links in links_by_type.items():
        logger.info(f"检查类型 {key} 的 {len(type_links)} 个链接")

        # 获取该类型的已下载文件名集合
        downloaded_filenames = downloaded_files_by_type.get(key, set())

        # 如果该类型没有已下载的文件，尝试使用“其他”分类
        if not downloaded_filenames:
            # 尝试匹配不带时间周期的key
            if "_" in key and key.count("_") >= 2:  # 如果key包含时间周期
                base_key = "_".join(key.split("_")[:2])  # 取市场类型和数据类型部分
                downloaded_filenames = downloaded_files_by_type.get(base_key, set())

            # 如果还是没有找到，尝试使用所有已下载的文件名
            if not downloaded_filenames:
                all_downloaded_filenames = set()
                for filenames in downloaded_files_by_type.values():
                    all_downloaded_filenames.update(filenames)
                downloaded_filenames = all_downloaded_filenames

        # 检查哪些链接对应的文件尚未下载
        for link in type_links:
            filename = get_filename_from_url(link)
            if filename not in downloaded_filenames:
                missing_links.append(link)

    logger.info(f"发现 {len(missing_links)} 个链接对应的文件尚未下载")
    return missing_links

def save_missing_links_to_csv(missing_links, output_file):
    """将缺失的链接保存到CSV文件中"""
    if not missing_links:
        logger.info("没有缺失的链接，不生成CSV文件")
        return

    # 创建输出目录（如果不存在）
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 将缺失的链接保存到CSV文件中
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['市场类型', '数据类型', '时间周期', '文件名', '链接'])

        for link in missing_links:
            market = get_market_from_url(link)
            data_type = get_data_type_from_url(link)
            interval = get_interval_from_url(link)
            filename = get_filename_from_url(link)
            writer.writerow([market, data_type, interval or "", filename, link])

    logger.info(f"已将 {len(missing_links)} 个缺失的链接保存到 {output_file}")

def main():
    """主函数"""
    try:
        logger.info("币安数据下载检查工具启动")
        logger.info(f"链接文件目录: {LINKS_DIR}")
        logger.info(f"下载文件目录: {DOWNLOAD_DIR}")
        logger.info(f"输出文件: {OUTPUT_FILE}")
        logger.info(f"是否包含子目录: {INCLUDE_SUBDIRS}")
        if MARKET_FILTER:
            logger.info(f"市场类型过滤: {MARKET_FILTER}")

        # 检查目录是否存在
        if not os.path.exists(LINKS_DIR):
            logger.error(f"链接文件目录 {LINKS_DIR} 不存在")
            return

        if not os.path.exists(DOWNLOAD_DIR):
            logger.error(f"下载文件目录 {DOWNLOAD_DIR} 不存在")
            return

        # 查找所有txt文件
        txt_files = find_txt_files(LINKS_DIR, True)

        if not txt_files:
            logger.warning(f"在 {LINKS_DIR} 目录下没有找到任何txt文件")
            return

        logger.info(f"找到 {len(txt_files)} 个txt文件")

        # 从所有txt文件中提取链接
        all_links = []
        for txt_file in txt_files:
            links = extract_links_from_file(txt_file)
            all_links.extend(links)

        # 去除重复链接
        unique_links = list(set(all_links))
        logger.info(f"总共提取了 {len(all_links)} 个链接，去重后剩余 {len(unique_links)} 个链接")

        # 按市场类型过滤链接
        if MARKET_FILTER:
            unique_links = filter_links_by_market(unique_links, MARKET_FILTER)

        # 检查哪些链接对应的文件尚未下载
        missing_links = check_downloads(unique_links, DOWNLOAD_DIR, INCLUDE_SUBDIRS)

        # 将缺失的链接保存到CSV文件中
        save_missing_links_to_csv(missing_links, OUTPUT_FILE)

        if missing_links:
            logger.info(f"检查完成，发现 {len(missing_links)} 个链接对应的文件尚未下载")
            logger.info(f"缺失的链接已保存到 {OUTPUT_FILE}")

            # 按数据类型和时间周期统计缺失文件
            missing_by_type = {}
            for link in missing_links:
                data_type = get_data_type_from_url(link)
                market_type = get_market_from_url(link)
                interval = get_interval_from_url(link)

                # 对于klines类型的数据，将时间周期也加入key
                if data_type in ["klines", "indexPriceKlines", "markPriceKlines", "premiumIndexKlines"] and interval:
                    key = f"{market_type}_{data_type}_{interval}"
                else:
                    key = f"{market_type}_{data_type}"

                if key not in missing_by_type:
                    missing_by_type[key] = 0
                missing_by_type[key] += 1

            # 输出统计信息
            logger.info("缺失文件按类型统计:")
            for key, count in sorted(missing_by_type.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  - {key}: {count} 个文件")
        else:
            logger.info("检查完成，所有链接对应的文件均已下载")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
