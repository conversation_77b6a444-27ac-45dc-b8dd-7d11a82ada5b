import os
import re
import sys
from datetime import datetime

def extract_info_from_log(log_file_path):
    """从日志文件中提取关键信息"""
    if not os.path.exists(log_file_path):
        print(f"错误: 日志文件 {log_file_path} 不存在!")
        return None
    
    try:
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
    except Exception as e:
        print(f"读取日志文件时出错: {e}")
        return None
    
    # 提取信息
    info = {
        'started': False,
        'finished': False,
        'error': False,
        'pairs_found': 0,
        'pairs_processed': [],
        'files_found': 0,
        'last_processed_pair': '',
        'error_messages': [],
        'last_timestamp': None
    }
    
    # 检查爬虫是否开始
    if "开始爬取币安期货市场数据" in log_content:
        info['started'] = True
    
    # 检查爬虫是否完成
    if "爬取完成！共找到" in log_content:
        info['finished'] = True
        
        # 提取总计数据
        match = re.search(r'爬取完成！共找到\s+(\d+)\s+个币对，\s*(\d+)\s+个数据文件', log_content)
        if match:
            info['pairs_found'] = int(match.group(1))
            info['files_found'] = int(match.group(2))
    
    # 检查是否发生错误
    if "发生错误:" in log_content:
        info['error'] = True
        error_matches = re.findall(r'发生错误:\s*(.+)', log_content)
        info['error_messages'].extend(error_matches)
    
    # 提取已处理的币对
    processed_pairs = re.findall(r'处理币对\s+\d+/\d+:\s+([^\s]+)', log_content)
    info['pairs_processed'] = processed_pairs
    
    if processed_pairs:
        info['last_processed_pair'] = processed_pairs[-1]
    
    # 提取每个币对找到的文件数
    pair_files = {}
    for pair in set(processed_pairs):
        # 查找该币对的文件数量信息
        matches = re.findall(rf'找到\s+(\d+)\s+个数据文件.*?{pair}', log_content)
        if matches:
            pair_files[pair] = int(matches[-1])  # 使用最后一次提到的数量
    
    info['pair_files'] = pair_files
    
    # 提取最后时间戳
    timestamp_matches = re.findall(r'\[(\d{2}:\d{2}:\d{2})\.\d+\]', log_content)
    if timestamp_matches:
        info['last_timestamp'] = timestamp_matches[-1]
    
    return info

def analyze_log(log_info):
    """分析日志信息并给出结论"""
    if not log_info:
        return "无法分析日志文件。"
    
    analysis = []
    
    # 爬虫状态
    if not log_info['started']:
        analysis.append("❌ 爬虫尚未开始运行。")
    elif log_info['finished']:
        analysis.append(f"✅ 爬虫已成功完成，共获取 {log_info['pairs_found']} 个币对，{log_info['files_found']} 个数据文件。")
    elif log_info['error']:
        analysis.append("❌ 爬虫运行过程中发生错误，未能正常完成。")
        for error in log_info['error_messages']:
            analysis.append(f"   错误信息: {error}")
    else:
        analysis.append("⚠️ 爬虫已开始运行但尚未完成。")
    
    # 处理进度
    if log_info['pairs_processed']:
        total_processed = len(log_info['pairs_processed'])
        unique_processed = len(set(log_info['pairs_processed']))
        
        analysis.append(f"📊 共处理了 {unique_processed} 个不同币对，总处理次数 {total_processed}。")
        analysis.append(f"🔍 最后处理的币对是: {log_info['last_processed_pair']}")
        
        # 分析文件获取情况
        pairs_with_files = sum(1 for count in log_info['pair_files'].values() if count > 0)
        total_files = sum(log_info['pair_files'].values())
        
        if pairs_with_files > 0:
            analysis.append(f"📁 成功获取文件的币对: {pairs_with_files}/{unique_processed} ({pairs_with_files/unique_processed:.1%})")
            analysis.append(f"📁 共获取到 {total_files} 个数据文件，平均每个币对 {total_files/pairs_with_files:.1f} 个文件。")
        else:
            analysis.append("⚠️ 未能成功获取任何数据文件。")
    else:
        analysis.append("⚠️ 未检测到任何币对处理记录。")
    
    # 时间信息
    if log_info['last_timestamp']:
        analysis.append(f"⏱️ 最后活动时间: {log_info['last_timestamp']}")
    
    # 给出建议
    if not log_info['finished'] and not log_info['error'] and log_info['started']:
        analysis.append("\n🔄 建议: 爬虫可能仍在运行中，可以继续等待。")
    elif log_info['error']:
        analysis.append("\n🔄 建议: 检查错误信息，修复问题后重新运行爬虫。")
    elif not log_info['started']:
        analysis.append("\n🔄 建议: 运行爬虫脚本 binance_crawler.py。")
    else:
        analysis.append("\n🔄 建议: 运行验证脚本 verify_results.py 检查抓取结果的完整性。")
    
    return "\n".join(analysis)

def main():
    # 默认日志文件路径或从命令行参数获取
    log_file = "crawler_log.txt" if len(sys.argv) < 2 else sys.argv[1]
    
    print(f"正在分析日志文件: {log_file}...")
    log_info = extract_info_from_log(log_file)
    
    if log_info:
        analysis = analyze_log(log_info)
        print("\n" + "="*50)
        print("日志分析结果")
        print("="*50)
        print(analysis)
        print("="*50)
    else:
        print("无法提取日志信息，请确保日志文件存在且格式正确。")

if __name__ == "__main__":
    main() 