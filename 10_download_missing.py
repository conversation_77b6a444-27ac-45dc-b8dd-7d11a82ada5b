#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
币安数据缺失文件下载工具

这个脚本用于下载missing_links.csv中的缺失文件，并自动放置在对应的文件夹中。
它会根据链接中的市场类型、数据类型和时间周期自动创建相应的文件夹结构。

使用方法:
1. 运行脚本: python 10_download_missing.py [--input INPUT_FILE] [--output OUTPUT_DIR] [--workers NUM] [--timeout SEC] [--retries NUM]
"""

import os
import re
import csv
import requests
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import time
import logging
from urllib.parse import urlparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 配置参数
CONFIG = {
    "input_file": "missing_links.csv",           # 缺失链接文件
    "output_dir": "Z:\\Binance",                 # 下载文件保存目录
    "max_workers": 50,                           # 并发线程数
    "max_retries": 3,                            # 单个文件重试次数
    "timeout": 10,                               # 单个请求超时时间（秒）
    "download_errors_log": "missing_download_errors.log",  # 下载失败日志
    "headers": {                                 # 请求头设置
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
}

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="币安数据缺失文件下载工具")
    parser.add_argument("--input", help=f"缺失链接文件，默认为 {CONFIG['input_file']}")
    parser.add_argument("--output", help=f"下载文件保存目录，默认为 {CONFIG['output_dir']}")
    parser.add_argument("--workers", type=int, help=f"并发线程数，默认为 {CONFIG['max_workers']}")
    parser.add_argument("--timeout", type=int, help=f"请求超时时间（秒），默认为 {CONFIG['timeout']}")
    parser.add_argument("--retries", type=int, help=f"重试次数，默认为 {CONFIG['max_retries']}")
    return parser.parse_args()

def read_missing_links(file_path):
    """从CSV文件中读取缺失的链接"""
    missing_links = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过标题行
            for row in reader:
                if len(row) >= 5:  # 市场类型,数据类型,时间周期,文件名,链接
                    market_type, data_type, interval, filename, url = row
                    missing_links.append({
                        'market_type': market_type,
                        'data_type': data_type,
                        'interval': interval,
                        'filename': filename,
                        'url': url
                    })
                elif len(row) >= 4:  # 兼容旧格式：市场类型,数据类型,文件名,链接
                    market_type, data_type, filename, url = row
                    missing_links.append({
                        'market_type': market_type,
                        'data_type': data_type,
                        'interval': '',
                        'filename': filename,
                        'url': url
                    })
        logger.info(f"从 {file_path} 中读取了 {len(missing_links)} 个缺失链接")
    except Exception as e:
        logger.error(f"读取缺失链接文件时出错: {e}")
    return missing_links

def get_target_dir(link_info, base_dir):
    """根据链接信息生成目标目录"""
    market_type = link_info['market_type']
    data_type = link_info['data_type']
    interval = link_info['interval']
    url = link_info['url']
    
    # 从URL中提取更多信息
    url_parts = urlparse(url).path.split('/')
    frequency = "monthly" if "monthly" in url else "daily"
    
    # 标准化市场类型
    if market_type == "spot":
        market_dir = "spot"
    elif market_type == "futures_um":
        market_dir = "futures-um"
    elif market_type == "futures_cm":
        market_dir = "futures-cm"
    else:
        market_dir = market_type.replace("_", "-")
    
    # 构建目录结构
    if interval and data_type in ["klines", "indexPriceKlines", "markPriceKlines", "premiumIndexKlines"]:
        # 对于带时间周期的K线数据
        target_dir = os.path.join(base_dir, f"{market_dir}-{frequency}-{data_type}-{interval}")
    else:
        # 对于其他数据类型
        target_dir = os.path.join(base_dir, f"{market_dir}-{frequency}-{data_type}")
    
    return target_dir

def download_file(link_info, base_dir):
    """下载单个文件并保存到对应目录"""
    url = link_info['url']
    filename = link_info['filename']
    
    # 获取目标目录
    target_dir = get_target_dir(link_info, base_dir)
    
    # 创建目标目录（如果不存在）
    os.makedirs(target_dir, exist_ok=True)
    
    # 目标文件路径
    target_path = os.path.join(target_dir, filename)
    
    # 如果文件已存在且大小大于0，则跳过
    if os.path.exists(target_path) and os.path.getsize(target_path) > 0:
        return url, True, f"文件已存在: {target_path}"
    
    # 尝试下载文件
    for attempt in range(CONFIG['max_retries']):
        try:
            with requests.Session() as session:
                with session.get(url, stream=True, timeout=CONFIG['timeout'], headers=CONFIG['headers']) as r:
                    r.raise_for_status()
                    
                    # 保存文件
                    with open(target_path, 'wb') as f:
                        for chunk in r.iter_content(chunk_size=8192):
                            f.write(chunk)
            
            return url, True, f"下载成功: {target_path}"
        except Exception as e:
            if attempt == CONFIG['max_retries'] - 1:
                return url, False, f"下载失败: {str(e)}"
            time.sleep(1)  # 失败后等待1秒重试

def batch_download_missing(missing_links, base_dir):
    """批量下载缺失的文件"""
    if not missing_links:
        logger.info("没有缺失的链接需要下载")
        return
    
    # 确保基础目录存在
    os.makedirs(base_dir, exist_ok=True)
    
    # 创建线程池
    with ThreadPoolExecutor(max_workers=CONFIG['max_workers']) as executor:
        # 提交下载任务
        futures = {executor.submit(download_file, link, base_dir): link for link in missing_links}
        
        # 进度条监控
        success_count = 0
        error_log = []
        
        with tqdm(total=len(missing_links), desc="下载进度") as pbar:
            for future in as_completed(futures):
                url, success, message = future.result()
                if success:
                    success_count += 1
                else:
                    link_info = next(link for link in missing_links if link['url'] == url)
                    error_log.append(f"{url}\t{link_info['market_type']}\t{link_info['data_type']}\t{link_info['interval']}\t{message}")
                pbar.update(1)
                pbar.set_postfix_str(f"成功: {success_count} 失败: {len(error_log)}")
    
    # 保存错误日志
    if error_log:
        with open(CONFIG['download_errors_log'], "w", encoding='utf-8') as f:
            f.write("\n".join(error_log))
        logger.info(f"\n{len(error_log)}个文件下载失败，详见 {CONFIG['download_errors_log']}")
    else:
        logger.info("所有文件下载成功")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 更新配置
    if args.input:
        CONFIG['input_file'] = args.input
    if args.output:
        CONFIG['output_dir'] = args.output
    if args.workers:
        CONFIG['max_workers'] = args.workers
    if args.timeout:
        CONFIG['timeout'] = args.timeout
    if args.retries:
        CONFIG['max_retries'] = args.retries
    
    logger.info("币安数据缺失文件下载工具启动")
    logger.info(f"缺失链接文件: {CONFIG['input_file']}")
    logger.info(f"下载文件保存目录: {CONFIG['output_dir']}")
    logger.info(f"并发线程数: {CONFIG['max_workers']}")
    logger.info(f"请求超时时间: {CONFIG['timeout']}秒")
    logger.info(f"重试次数: {CONFIG['max_retries']}")
    
    # 读取缺失链接
    missing_links = read_missing_links(CONFIG['input_file'])
    
    # 批量下载缺失文件
    batch_download_missing(missing_links, CONFIG['output_dir'])
    
    logger.info("下载任务完成")

if __name__ == "__main__":
    main()
