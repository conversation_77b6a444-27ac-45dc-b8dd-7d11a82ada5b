# 币安数据爬虫与验证工具

这个项目包含一系列Python脚本，用于爬取币安数据市场的各类数据链接，并进行验证、下载和检查。项目支持爬取现货、U本位合约和币本位合约市场的多种数据类型，并提供完整的验证、补充爬取、文件下载和检查功能。

## 项目结构

项目包含以下主要模块（按使用顺序编号）：

- `01_binance_crawler.py` - 主爬虫脚本，用于爬取币安数据市场的各类数据
- `02_verify_results.py` - 简单的结果验证脚本，用于验证爬取结果的完整性
- `03_verify_completeness.py` - 高级验证脚本，支持单文件和批量验证模式，可与网站数据比对
- `04_supplementary_crawler.py` - 补充爬虫，用于处理缺失的币对
- `05_process_missing_pairs_with_intervals.py` - 处理带时间周期的缺失币对
- `06_split_links.py` - 链接提取与分割工具，用于将链接分批处理
- `07_download_tool.py` - 文件下载工具，用于并发下载数据文件
- `08_check_downloads.py` - 下载检查工具，用于验证文件是否完整下载
- `09_log_analyzer.py` - 日志分析工具，用于分析爬虫日志并提供建议

## 模块关联度

各模块之间的关联如下：

1. `01_binance_crawler.py` 是核心模块，实现了爬取功能，其他模块依赖它的函数和常量
2. `02_verify_results.py` 和 `03_verify_completeness.py` 都用于验证爬取结果，但后者功能更强大
3. `03_verify_completeness.py` 依赖 `01_binance_crawler.py` 中的函数来与网站数据进行比对
4. `04_supplementary_crawler.py` 和 `05_process_missing_pairs_with_intervals.py` 用于补充爬取缺失的数据
5. `06_split_links.py` 用于将爬取到的链接分割成小批量，便于下载
6. `07_download_tool.py` 用于下载链接文件中的数据，支持并发下载和URL验证
7. `08_check_downloads.py` 用于检查下载文件是否完整，并生成缺失文件的报告
8. `09_log_analyzer.py` 可以分析爬虫日志，帮助用户了解爬取过程中的问题

## 功能

- 使用本地Chrome浏览器打开币安数据页面
- 爬取币安各个市场(现货、U本位合约、币本位合约)的数据
- 支持多种数据类型：K线数据、交易数据、聚合交易数据、订单深度、价格指数等
- 支持多种时间周期：1秒、1分钟、3分钟、5分钟、15分钟、30分钟、1小时、2小时、4小时、6小时、8小时、12小时、1天、3天、1周、1月等
- 支持日度数据和月度数据的爬取
- 保存所有链接到文本文件，并按币对分组
- 验证爬取结果的完整性，支持与网站数据比对
- 自动补充爬取缺失的数据
- 提供日志分析功能，帮助诊断爬取过程中的问题

## 前置条件

- 已安装Google Chrome浏览器
- Chrome浏览器路径默认为：`C:\Program Files\Google\Chrome\Application\chrome.exe`
- 如果您的Chrome路径不同，请修改脚本中的`CHROME_PATH`变量或通过命令行参数指定
- Python 3.6+

## 安装依赖

安装必要的Python依赖包:

```bash
pip install -r requirements.txt
```

主要依赖包括：
- selenium - 用于浏览器自动化
- tqdm - 用于显示进度条
- requests - 用于HTTP请求

## 使用方法

### 1. 爬取数据

```bash
python binance_crawler.py [--market MARKET] [--data DATA_TYPE] [--intervals INTERVALS] [--frequency FREQUENCY] [--multi] [--output OUTPUT_DIR] [--chrome CHROME_PATH]
```

参数说明：
- `--market` 或 `-m`: 市场类型，可选值为 `spot`(现货), `futures/um`(U本位合约), `futures/cm`(币本位合约)
- `--data` 或 `-d`: 数据类型，可选值为 `klines`(K线), `trades`(交易), `aggTrades`(聚合交易), `bookDepth`(深度), `indexPriceKlines`(价格指数K线), `markPriceKlines`(标记价格K线)等
- `--intervals` 或 `-i`: 时间周期（仅对K线数据有效），可选值为 `1s`, `1m`, `3m`, `5m`, `15m`, `30m`, `1h`, `2h`, `4h`, `6h`, `8h`, `12h`, `1d`, `3d`, `1w`, `1mo`等
- `--frequency` 或 `-f`: 数据频率，可选值为 `daily`(每日数据), `monthly`(月度数据)
- `--multi`: 启用多数据类型爬取模式，此时会使用配置文件中的数据类型列表
- `--output` 或 `-o`: 指定输出目录
- `--chrome` 或 `-c`: 指定Chrome浏览器路径

例如，爬取U本位合约的日线K线数据：
```bash
python binance_crawler.py --market futures/um --data klines --intervals 1d --frequency monthly
```

爬取现货市场的交易数据：
```bash
python binance_crawler.py --market spot --data trades --frequency monthly
```

爬取多种数据类型（使用配置文件中的数据类型列表）：
```bash
python binance_crawler.py --market futures/cm --multi --frequency daily
```

### 2. 验证爬取结果

#### 简单验证

```bash
python verify_results.py
```

这个脚本会验证默认的结果文件，并生成验证报告。

#### 高级验证

```bash
python verify_completeness.py [--mode MODE] [--file FILE] [--market MARKET] [--data DATA_TYPE] [--frequency FREQUENCY]
```

参数说明：
- `--mode` 或 `-m`: 验证模式，可选值为 `single`(单文件模式), `all`(批量模式)
- `--file` 或 `-f`: 要验证的文件名（单文件模式下使用）
- `--market`: 市场类型，用于与网站数据比对
- `--data`: 数据类型，用于与网站数据比对
- `--frequency`: 数据频率，用于与网站数据比对

单文件验证模式：
```bash
python verify_completeness.py -m single -f futures_um_monthly_aggTrades_all_links.txt
```

批量验证模式（验证目录下所有结果文件）：
```bash
python verify_completeness.py -m all
```

### 3. 补充爬取缺失数据

```bash
python supplementary_crawler.py [--file FILE] [--auto] [--batch] [--market MARKET] [--data DATA_TYPE] [--frequency FREQUENCY]
```

参数说明：
- `--file` 或 `-f`: 要处理的验证结果文件
- `--auto` 或 `-a`: 自动模式，自动查找最新的验证结果并处理
- `--batch` 或 `-b`: 批量模式，处理所有缺失数据
- `--market`: 市场类型
- `--data`: 数据类型
- `--frequency`: 数据频率

自动模式（推荐）：
```bash
python supplementary_crawler.py --auto
```

指定文件模式：
```bash
python supplementary_crawler.py -f verification_summary_20250502.json
```

### 4. 链接分割

```bash
python 06_split_links.py
```

这个脚本会从指定目录中提取所有链接，并将它们分割成每个文件最多包含9999个链接的小文件，便于下载。你可以在脚本开头的配置部分修改参数：

```python
# 配置参数
CONFIG = {
    "links_dir": "binance_links",  # 链接文件目录
    "output_dir": "download_links",  # 输出目录
    "max_links_per_file": 9999,  # 每个文件的最大链接数
    "classify_by_market": False  # 是否按市场类型分类
}
```

### 5. 下载数据文件

```bash
python 07_download_tool.py [--resume] [--workers NUM] [--timeout SEC] [--retries NUM]
```

参数说明：
- `--resume`: 启用断点续传模式，重新下载失败的文件
- `--workers`: 设置并发线程数，默认为50
- `--timeout`: 设置请求超时时间（秒），默认为10
- `--retries`: 设置重试次数，默认为3

这个脚本会从`H:\binance_crawler\download_links`目录下的所有txt文件中提取链接，并并发下载到`H:\binance_crawler\downloads`目录。脚本会验证URL的有效性，并将无效的URL记录到日志文件中。

#### 正常下载模式
```bash
python 07_download_tool.py
```

#### 断点续传模式（重新下载失败的文件）
```bash
python 07_download_tool.py --resume
```

#### 自定义参数
```bash
python 07_download_tool.py --workers 100 --timeout 30 --retries 5
```

你可以在脚本开头的配置部分修改参数：

```python
# 配置参数
CONFIG = {
    "links_dir": "H:\\binance_crawler\\download_links",  # 链接文件目录
    "output_dir": "H:\\binance_crawler\\downloads",     # 下载文件保存目录
    "max_workers": 50,                              # 并发线程数
    "max_retries": 3,                               # 单个文件重试次数
    "timeout": 10,                                  # 单个请求超时时间（秒）
    "url_pattern": r'^https?://[^\s/$.?#].[^\s]*$',   # URL正则表达式
    "error_log": "H:\\binance_crawler\\invalid_urls.log",  # 无效内容日志
    "download_errors_log": "H:\\binance_crawler\\download_errors.log",  # 下载失败日志
    "resume_mode": False,                           # 是否为断点续传模式
}
```

### 6. 检查下载文件

```bash
python 08_check_downloads.py
```

这个脚本用于检查下载文件是否完整，并将缺失的链接汇总到一个表格中。它会比较原始链接文件和已下载的文件，找出哪些链接对应的文件尚未下载。

脚本的主要特点：

- **支持不同时间周期的klines数据**：可以正确区分和处理不同时间周期（1d、1m等）的klines数据
- **支持不同类型的klines数据**：可以正确区分普通klines、indexPriceKlines、markPriceKlines等不同类型
- **支持多种文件夹结构**：可以处理各种命名格式的文件夹，如`futures-um-monthly-klines-1d`
- **多编码支持**：可以处理不同编码的文件，避免编码问题导致的链接提取失败
- **详细的统计信息**：提供按数据类型、市场类型和时间周期分组的缺失文件统计

你可以在脚本开头的配置部分修改参数：

```python
# 配置部分 - 请在这里修改参数
LINKS_DIR = "binance_links"  # 链接文件目录
DOWNLOAD_DIR = "Z:\\Binance"  # 下载文件目录
OUTPUT_FILE = "missing_links.csv"  # 输出文件
INCLUDE_SUBDIRS = True  # 是否包含子目录
MARKET_FILTER = None  # 市场类型过滤
```

脚本输出的CSV文件包含以下列：
- 市场类型（如spot、futures_um等）
- 数据类型（如klines、trades、indexPriceKlines等）
- 时间周期（如1d、1m等，仅对klines类型数据）
- 文件名
- 链接

**注意**：当处理大量文件时，脚本可能需要较长时间来扫描目录和分析文件。请耐心等待。

### 7. 分析日志

```bash
python 09_log_analyzer.py [--log LOG_FILE]
```

参数说明：
- `--log` 或 `-l`: 要分析的日志文件路径

如果不指定日志文件，将自动查找最新的日志文件进行分析：
```bash
python 09_log_analyzer.py
```

指定日志文件：
```bash
python 09_log_analyzer.py -l logs/crawler_20250502_112345.log
```

## 输出格式

### 普通数据类型输出格式

链接以币对为分组保存，格式如下:

```
=== BTCUSDT ===
https://data.binance.vision/data/futures/um/monthly/trades/BTCUSDT/file1.zip
https://data.binance.vision/data/futures/um/monthly/trades/BTCUSDT/file2.zip
...

=== ETHUSDT ===
https://data.binance.vision/data/futures/um/monthly/trades/ETHUSDT/file1.zip
...
```

### K线数据类型输出格式

对于K线数据，还会按时间周期进行分组：

```
=== 时间周期: 1d ===

=== BTCUSDT ===
https://data.binance.vision/data/futures/um/monthly/klines/BTCUSDT/1d/file1.zip
https://data.binance.vision/data/futures/um/monthly/klines/BTCUSDT/1d/file2.zip
...

=== ETHUSDT ===
https://data.binance.vision/data/futures/um/monthly/klines/ETHUSDT/1d/file1.zip
...

=== 时间周期: 1h ===

=== BTCUSDT ===
https://data.binance.vision/data/futures/um/monthly/klines/BTCUSDT/1h/file1.zip
...
```

### 新增数据输出格式

每次爬取都会生成一个新增数据文件，记录本次爬取新增的数据链接，格式与上述相同。文件名格式为：
`{market_type}_{data_frequency}_{data_type}_new_data_{timestamp}.txt`

## 注意事项

### 爬虫脚本

- 脚本使用可见模式运行Chrome，会显示浏览器窗口
- 每处理10个币对会暂停一段时间，避免请求过快
- 如果遇到问题，请确保Chrome浏览器正常运行且路径正确
- 爬虫会自动记录日志到`logs`目录，可以使用`09_log_analyzer.py`分析日志
- 支持多种数据类型的批量爬取，可以通过修改配置文件中的`data_types_list`来设置

### 验证脚本

- `02_verify_results.py` 只进行本地验证，不会连接到币安网站
- `03_verify_completeness.py` 会连接到币安网站获取实时数据进行比对
- 批量验证模式下只生成一个汇总的JSON报告文件
- 单文件验证模式下会为每个文件生成单独的验证报告
- 验证结果会保存在`binance_links`目录下，文件名格式为`verification_summary_{timestamp}.json`

### 补充爬虫

- `04_supplementary_crawler.py`可以自动处理验证结果中发现的缺失数据
- 推荐使用`--auto`参数，自动查找最新的验证结果并处理
- 对于K线数据，会使用`05_process_missing_pairs_with_intervals.py`处理带时间周期的缺失币对

### 链接分割与下载

- `06_split_links.py` 可以将大量链接分割成小批量，每个文件默认包含9999个链接
- `07_download_tool.py` 支持并发下载，可以通过调整`max_workers`参数来控制并发线程数
- **断点续传功能**：使用`--resume`参数或设置`resume_mode=True`可启用断点续传模式，自动跳过已下载的文件并重新下载失败的文件
- **URL验证**：下载工具会验证URL的有效性，并将无效的URL记录到`invalid_urls.log`文件中
- **错误日志**：下载失败的文件会记录到`download_errors.log`文件中，方便后续重试
- **自动创建目录**：脚本会自动创建必要的目录结构，无需手动创建
- **命令行参数**：支持通过命令行参数调整并发线程数、超时时间、重试次数等参数

### 下载检查

- `08_check_downloads.py` 可以检查下载文件是否完整，并生成缺失文件的报告
- **时间周期识别**：脚本能够识别和处理不同时间周期（1d、1m等）的klines数据，避免混淆
- **数据类型区分**：能够正确区分普通klines、indexPriceKlines、markPriceKlines等不同类型
- **多种文件夹结构支持**：可以处理各种命名格式的文件夹，如`futures-um-monthly-klines-1d`
- **多编码支持**：支持多种编码的文件读取（utf-8、gbk、latin1等），可以处理编码问题
- **详细统计**：缺失的链接会保存到CSV文件中，包含市场类型、数据类型、时间周期、文件名和链接信息
- **大文件处理**：当处理大量文件时，脚本可能需要较长时间来扫描目录和分析文件，请耐心等待

### 配置选项

- 所有脚本都支持通过修改源代码开头的配置部分来自定义参数
- 在`01_binance_crawler.py`中，可以修改`DEFAULT_CONFIG`中的`data_types_list`来设置多数据类型爬取的数据类型
- 在`03_verify_completeness.py`中，可以通过修改`DEFAULT_CONFIG`中的`"mode"`值来切换默认的验证模式
- 在`07_download_tool.py`中，可以调整以下参数：
  - `max_workers`：并发线程数，控制同时下载的文件数量
  - `max_retries`：单个文件的重试次数
  - `timeout`：请求超时时间
  - `resume_mode`：是否启用断点续传模式
  - `url_pattern`：URL验证的正则表达式
- 在`08_check_downloads.py`中，可以调整以下参数：
  - `LINKS_DIR`：链接文件目录
  - `DOWNLOAD_DIR`：下载文件目录
  - `OUTPUT_FILE`：输出文件路径
  - `INCLUDE_SUBDIRS`：是否包含子目录
  - `MARKET_FILTER`：市场类型过滤

### 数据类型适配度

不同的数据类型适用于不同的市场和频率：

- 全适用的数据类型（3个）：K线数据(klines)、交易数据(trades)、聚合交易数据(aggTrades)
- 合约专用但日度月度均适用（4个）：最优挂单(bookTicker)、价格指数K线(indexPriceKlines)、标记价格K线(markPriceKlines)、溢价指数K线(premiumIndex)
- 月度合约专用（1个）：资金费率(funding)
- 日度合约专用（2个）：订单深度(depth)、合约指标(metrics)
- 日度币本位合约专用（1个）：爆仓清算快照(liquidation)
- 期权专用（2个）：波动率指数(bvol)、期权EOH指数K线(eoh)

## 常见问题与解决方案

### 1. 下载工具相关问题

#### 问题：下载过程中断，如何继续下载？

解决方案：使用断点续传模式重新启动下载工具：
```bash
python 07_download_tool.py --resume
```
或者在脚本中设置`resume_mode = True`然后直接运行脚本。

#### 问题：下载速度过慢

解决方案：
1. 增加并发线程数：`python 07_download_tool.py --workers 100`
2. 减少超时时间，使失败的请求更快重试：`python 07_download_tool.py --timeout 5`
3. 如果网络不稳定，可以增加重试次数：`python 07_download_tool.py --retries 5`

### 2. 检查下载工具相关问题

#### 问题：脚本运行时间过长

解决方案：
1. 如果不需要扫描子目录，可以将`INCLUDE_SUBDIRS`设置为`False`
2. 可以使用`MARKET_FILTER`参数只检查特定市场类型的文件
3. 将下载目录分成多个小目录，分别运行脚本

#### 问题：无法正确识别文件夹结构

解决方案：
1. 确保文件夹命名格式符合预期，如`futures-um-monthly-klines-1d`
2. 如果使用了自定义的文件夹结构，可能需要修改脚本中的路径解析逻辑

### 3. 编码问题

#### 问题：链接文件编码错误

解决方案：
1. 脚本已经支持多种编码（utf-8、gbk、latin1等），应该能自动处理大多数编码问题
2. 如果仍然出现编码错误，可以尝试将文件转换为UTF-8编码：
   ```bash
   # 在Windows上使用PowerShell
   Get-Content -Path 原文件.txt -Encoding 原编码 | Set-Content -Path 新文件.txt -Encoding utf8
   ```

### 4. 其他问题

#### 问题：爬虫脚本无法打开Chrome浏览器

解决方案：
1. 确保Chrome浏览器已安装并可以正常运行
2. 使用`--chrome`参数指定Chrome浏览器的路径：
   ```bash
   python 01_binance_crawler.py --chrome "C:\Program Files\Google\Chrome\Application\chrome.exe"
   ```
3. 检查脚本中的`CHROME_PATH`变量是否正确

#### 问题：内存不足

解决方案：
1. 减少并发线程数：`python 07_download_tool.py --workers 20`
2. 将大量链接分成多个小文件，分批处理
3. 关闭其他占用内存的应用程序