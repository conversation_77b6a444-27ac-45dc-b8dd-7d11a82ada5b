#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
演示不同的批量处理模式
"""

import os
import shutil
import time
from find_duplicate_files import CONFIG, find_duplicates

def create_demo_environment():
    """创建演示环境"""
    test_base = "demo_batch_modes"
    if os.path.exists(test_base):
        shutil.rmtree(test_base)
    
    # 创建目录结构
    folders = [
        "重要文件",
        "temp/临时文件",      # 明确应该删除
        "下载/新下载",        # 明确应该删除
        "backup/备份",        # 应该保留
        "普通目录1",
        "普通目录2",
    ]
    
    for folder in folders:
        full_path = os.path.join(test_base, folder)
        os.makedirs(full_path, exist_ok=True)
    
    # 创建测试文件
    test_content = "这是测试文件的内容"
    
    test_files = [
        # 明确应该自动删除的情况
        ("重要文件/document.txt", test_content),
        ("temp/临时文件/document.txt", test_content),  # temp目录，应该自动删除
        
        ("backup/备份/report.txt", test_content),
        ("下载/新下载/report.txt", test_content),      # 下载目录，应该自动删除
        
        # 副本文件名模式，应该自动删除
        ("重要文件/data.xlsx", test_content),
        ("普通目录1/data(1).xlsx", test_content),      # 副本模式，应该自动删除
        
        # 需要用户确认的情况
        ("普通目录1/uncertain.txt", test_content),
        ("普通目录2/uncertain.txt", test_content),     # 两个普通目录，需要确认
        
        ("重要文件/another.txt", test_content),
        ("普通目录1/another.txt", test_content),       # 需要确认
        
        # 更多文件用于批量测试
        ("重要文件/file1.txt", test_content),
        ("temp/临时文件/file1.txt", test_content),
        
        ("重要文件/file2.txt", test_content),
        ("temp/临时文件/file2.txt", test_content),
        
        ("重要文件/file3.txt", test_content),
        ("下载/新下载/file3.txt", test_content),
    ]
    
    for file_path, content in test_files:
        full_path = os.path.join(test_base, file_path)
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(content)
    
    print(f"演示环境已创建在: {test_base}")
    print("包含以下类型的重复文件:")
    print("  - 明确应该自动删除的 (temp、下载目录)")
    print("  - 副本文件名模式")
    print("  - 需要用户确认的普通重复文件")
    
    return test_base

def demo_mode(mode_name, mode_config):
    """演示指定模式"""
    print(f"\n" + "="*70)
    print(f"演示模式: {mode_name}")
    print("="*70)
    
    # 创建测试环境
    test_dir = create_demo_environment()
    
    # 备份原始配置
    original_config = CONFIG.copy()
    
    try:
        # 修改配置
        CONFIG['scan_directories'] = [test_dir]
        CONFIG['cross_folder_comparison'] = True
        CONFIG['debug_mode'] = False
        CONFIG['verify_with_hash'] = False
        CONFIG['file_selection_strategy']['show_selection_reason'] = True
        CONFIG['delete_mode'].update(mode_config)
        
        print(f"当前配置:")
        print(f"  删除模式: {CONFIG['delete_mode']['mode']}")
        if 'batch_size' in mode_config:
            print(f"  批量大小: {CONFIG['delete_mode']['batch_size']}")
        
        input("\n按回车键开始演示...")
        find_duplicates()
        
    finally:
        # 恢复原始配置
        CONFIG.clear()
        CONFIG.update(original_config)
        
        # 清理测试环境
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"测试环境已清理: {test_dir}")

def main():
    """主函数"""
    print("重复文件处理模式演示")
    print("="*50)
    
    modes = {
        "1": ("智能自动模式", {'mode': 'smart_auto'}),
        "2": ("批量确认模式 (5个一批)", {'mode': 'batch_confirm', 'batch_size': 5}),
        "3": ("批量确认模式 (3个一批)", {'mode': 'batch_confirm', 'batch_size': 3}),
        "4": ("逐个确认模式", {'mode': 'confirm_each'}),
        "5": ("完全自动模式", {'mode': 'auto'}),
    }
    
    print("可用的演示模式:")
    for key, (name, _) in modes.items():
        print(f"  {key}. {name}")
    
    while True:
        choice = input("\n请选择要演示的模式 (1-5, q=退出): ").strip()
        
        if choice.lower() == 'q':
            print("退出演示")
            break
        
        if choice in modes:
            mode_name, mode_config = modes[choice]
            demo_mode(mode_name, mode_config)
            
            continue_demo = input("\n是否继续演示其他模式? (y/n): ").strip().lower()
            if continue_demo != 'y':
                break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
