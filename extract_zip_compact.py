#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
精简版ZIP解压脚本
将ZIP文件解压到指定目录，并使用合并的进度条显示进度
"""

import os
import sys
import zipfile
import logging
import time
import shutil
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor
from tqdm import tqdm
from notify import notify_push, NOTIFY_CONFIG

# 配置项
CONFIG = {
    # 路径配置
    'zip_dir': 'Z:/Binance',  # ZIP文件所在目录
    'extract_dir': 'H:/',  # 解压目标目录
    'log_file': 'extract_zip.log',  # 日志文件
    
    # 处理配置
    'max_workers': 4,  # 线程池大小
    'batch_size': 100,  # 批处理大小，每处理这么多文件更新一次进度条
    
    # 通知配置
    'notification': {
        'enabled': True,  # 是否启用通知
        'channel': 'wxpusher',  # 通知渠道
        'subject': 'ZIP解压完成通知',  # 通知主题
        'uids': '',  # wxpusher的用户UID
    }
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(CONFIG['log_file'], encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 禁用tqdm在文件中的输出
class TqdmToLogger:
    def __init__(self, logger, level=logging.INFO):
        self.logger = logger
        self.level = level
        self.last_msg = ""
    
    def write(self, buf):
        buf = buf.strip()
        if buf and buf != self.last_msg:
            self.last_msg = buf
            self.logger.log(self.level, buf)
    
    def flush(self):
        pass

def get_data_type_from_zip_path(zip_path):
    """从ZIP文件路径获取数据类型"""
    parts = Path(zip_path).parts
    for part in parts:
        if 'futures-um' in part:
            return part
    return None

def get_target_dir(data_type):
    """根据数据类型获取目标目录"""
    if not data_type:
        return None
    
    # 将数据类型转换为目标目录名
    target_dir = f"data-{data_type}"
    full_path = os.path.join(CONFIG['extract_dir'], target_dir)
    
    # 确保目标目录存在
    os.makedirs(full_path, exist_ok=True)
    return full_path

def extract_zip_file(zip_path, target_dir, processed_files):
    """解压单个ZIP文件到目标目录"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            # 获取ZIP文件中的所有文件
            file_list = zip_ref.namelist()
            
            # 解压所有CSV文件
            for file_name in file_list:
                if file_name.endswith('.csv'):
                    # 解压文件到临时位置
                    zip_ref.extract(file_name, path=target_dir)
                    
                    # 记录已处理文件
                    processed_files.append(file_name)
                    
                    # 记录日志（但不打印到控制台）
                    logger.debug(f"已解压并移动文件: {file_name} -> {os.path.join(target_dir, file_name)}")
        
        return True
    except Exception as e:
        logger.error(f"解压文件 {zip_path} 失败: {str(e)}")
        return False

def process_zip_files(zip_dir, data_type_filter=None):
    """处理指定目录中的所有ZIP文件"""
    start_time = time.time()
    
    # 获取所有ZIP文件
    all_zip_files = []
    for root, _, files in os.walk(zip_dir):
        for file in files:
            if file.endswith('.zip'):
                zip_path = os.path.join(root, file)
                
                # 如果指定了数据类型过滤器，则只处理匹配的文件
                if data_type_filter:
                    data_type = get_data_type_from_zip_path(zip_path)
                    if data_type != data_type_filter:
                        continue
                
                all_zip_files.append(zip_path)
    
    if not all_zip_files:
        logger.info(f"未找到ZIP文件在 {zip_dir}")
        return
    
    logger.info(f"找到 {len(all_zip_files)} 个ZIP文件")
    
    # 按数据类型分组处理
    zip_files_by_type = {}
    for zip_path in all_zip_files:
        data_type = get_data_type_from_zip_path(zip_path)
        if data_type:
            if data_type not in zip_files_by_type:
                zip_files_by_type[data_type] = []
            zip_files_by_type[data_type].append(zip_path)
    
    # 处理每种数据类型
    total_processed = 0
    total_failed = 0
    
    for data_type, zip_files in zip_files_by_type.items():
        target_dir = get_target_dir(data_type)
        if not target_dir:
            logger.warning(f"无法确定 {data_type} 的目标目录，跳过")
            continue
        
        logger.info(f"开始处理 {data_type} 类型的 {len(zip_files)} 个ZIP文件")
        
        # 创建进度条
        pbar = tqdm(
            total=len(zip_files),
            desc=f"解压ZIP到CSV ({data_type})",
            unit="文件"
        )
        
        processed_files = []
        failed_files = []
        
        # 使用线程池处理ZIP文件
        with ThreadPoolExecutor(max_workers=CONFIG['max_workers']) as executor:
            # 批量处理文件
            for i in range(0, len(zip_files), CONFIG['batch_size']):
                batch = zip_files[i:i+CONFIG['batch_size']]
                futures = {executor.submit(extract_zip_file, zip_path, target_dir, processed_files): zip_path for zip_path in batch}
                
                # 等待批处理完成
                for future in futures:
                    zip_path = futures[future]
                    try:
                        success = future.result()
                        if not success:
                            failed_files.append(zip_path)
                    except Exception as e:
                        logger.error(f"处理 {zip_path} 时出错: {str(e)}")
                        failed_files.append(zip_path)
                
                # 更新进度条
                pbar.update(len(batch))
        
        pbar.close()
        
        # 更新统计信息
        total_processed += len(processed_files)
        total_failed += len(failed_files)
        
        logger.info(f"{data_type} 处理完成: 成功解压 {len(processed_files)} 个文件, 失败 {len(failed_files)} 个文件")
    
    # 计算总耗时
    elapsed_time = time.time() - start_time
    logger.info(f"所有ZIP文件处理完成，总耗时: {elapsed_time:.2f}秒")
    logger.info(f"总计: 成功解压 {total_processed} 个文件, 失败 {total_failed} 个文件")
    
    # 发送通知
    if CONFIG['notification']['enabled']:
        content = f"""
ZIP文件解压完成
处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {elapsed_time:.2f}秒
总文件数: {len(all_zip_files)}
成功解压: {total_processed}
失败数量: {total_failed}
"""
        notify_push(
            content=content,
            channel=CONFIG['notification']['channel'],
            subject=CONFIG['notification']['subject'],
            uids=CONFIG['notification']['uids'],
        )
    
    return {
        'total': len(all_zip_files),
        'processed': total_processed,
        'failed': total_failed,
        'elapsed_time': elapsed_time
    }

def main():
    """主函数"""
    try:
        # 解析命令行参数
        data_type_filter = None
        if len(sys.argv) > 1:
            data_type_filter = sys.argv[1]
        
        # 处理ZIP文件
        result = process_zip_files(CONFIG['zip_dir'], data_type_filter)
        
        if result:
            print("\n解压结果摘要:")
            print(f"总文件数: {result['total']}")
            print(f"成功解压: {result['processed']}")
            print(f"失败数量: {result['failed']}")
            print(f"总耗时: {result['elapsed_time']:.2f}秒")
        
    except Exception as e:
        error_message = f"程序执行出错: {str(e)}"
        logger.error(error_message)
        
        # 发送错误通知
        if CONFIG['notification']['enabled']:
            notify_push(
                content=error_message,
                channel=CONFIG['notification']['channel'],
                subject=f"{CONFIG['notification']['subject']} - 错误",
                uids=CONFIG['notification']['uids'],
            )

if __name__ == "__main__":
    main()
