#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import hashlib
import logging
import time
import json
from datetime import datetime
from tqdm import tqdm
import shutil

# 配置项
CONFIG = {
    # 是否跨文件夹对比
    'cross_folder_comparison': True,

    # 要扫描的目录列表
    'scan_directories': [
        #'Z:\Binance', #zip路径
        #'W:\\', #图书馆
        #'H:\\',  #csv路径
        #'D:\\360Downloads', #baidudownload
        #'X:\\【资料库】柚子',
        #'U:\\',  #cpa
        #'X:\【研究】nynx', #nynx
        'X:\\', #量化投资
        #'Z:\饼圈合集',
        #'Z:\\',
        #可以添加多个目录W:\【azw3】, W:\【mobi】, W:\【epub】, W:\【txt】,W:\【pdf】,W:\【chm】
    ],

    # 删除模式配置
    'delete_mode': {
        # 删除模式: 'auto' | 'confirm_each' | 'batch_confirm' | 'smart_auto'
        'mode': 'batch_confirm',

        # 智能自动删除：当选择理由明确时自动删除，模糊时询问
        'smart_auto_patterns': [
            'temp',     # 临时目录
            'cache',    # 缓存目录
            '下载',     # 下载目录
            '副本',     # 副本目录
            '复制',     # 复制目录
            '文件名模式判断(高置信度)',  # 明确的文件名副本模式
            '文件名模式判断(中等置信度)',  # 中等置信度的文件名副本模式
        ],

        # 批量确认时每批处理的数量
        'batch_size': 10,
    },

    # 日志文件路径
    'log_file': 'duplicate_files_log.txt',

    # 重复文件名模式配置
    'duplicate_patterns': {
        # 严格模式：完全匹配的副本模式
        'strict_patterns': [
            r'\(\d+\)$',      # 匹配 "(1)", "(2)" 等
            r'副本$',         # 匹配 "副本"
            r'复制$',         # 匹配 "复制"
            r'copy$',         # 匹配 "copy"
            r'_\d+$',         # 匹配 "_1", "_2" 等
            r'_copy$',        # 匹配 "_copy"
            r'_副本$',        # 匹配 "_副本"
            r'_复制$',        # 匹配 "_复制"
        ],

        # 宽松模式：相似文件名模式（需要更仔细的判断）
        'loose_patterns': [
            r'[._-]\d+$',     # 匹配 ".1", "_1", "-1" 等
            r'[._-]v\d+$',    # 匹配 ".v1", "_v2", "-v3" 等
            r'[._-]ver\d+$',  # 匹配 ".ver1", "_ver2" 等
            r'[._-]backup$',  # 匹配 ".backup", "_backup" 等
            r'[._-]bak$',     # 匹配 ".bak", "_bak" 等
            r'[._-]old$',     # 匹配 ".old", "_old" 等
            r'[._-]new$',     # 匹配 ".new", "_new" 等
            r'[._-]temp$',    # 匹配 ".temp", "_temp" 等
            r'[._-]tmp$',     # 匹配 ".tmp", "_tmp" 等
        ],

        # 是否启用宽松模式（可能会有误判，建议配合哈希验证）
        'enable_loose_mode': True,

        # 宽松模式下是否强制要求哈希验证
        'loose_mode_require_hash': True,
    },

    # 是否计算文件哈希值进行额外验证（会降低速度但提高准确性）
    'verify_with_hash': False,

    # 调试模式：显示详细的处理信息
    'debug_mode': False,

    # 性能优化配置
    'performance': {
        # 最小文件大小（字节），小于此大小的文件将被忽略
        'min_file_size': 1024,  # 1KB

        # 最大文件大小（字节），大于此大小的文件将强制使用哈希验证
        'max_file_size_no_hash': 100 * 1024 * 1024,  # 100MB

        # 文件类型过滤（扩展名列表，为空则不过滤）
        'file_extensions': [],  # 例如: ['.txt', '.doc', '.pdf']

        # 排除的文件类型
        'exclude_extensions': ['.tmp', '.log', '.cache'],

        # 是否缓存文件哈希值
        'cache_hashes': True,

        # 哈希缓存文件路径
        'hash_cache_file': 'file_hash_cache.json',
    },

    # 文件选择策略配置
    'file_selection_strategy': {
        # 优先保留的目录模式（正则表达式，优先级从高到低）
        'priority_directories': [
            r'.*[/\\]原始[/\\].*',      # 包含"原始"的目录
            r'.*[/\\]master[/\\].*',    # 包含"master"的目录
            r'.*[/\\]main[/\\].*',      # 包含"main"的目录
            r'.*[/\\]backup[/\\].*',    # 包含"backup"的目录（较低优先级）
        ],

        # 优先删除的目录模式（正则表达式）
        'delete_priority_directories': [
            r'.*[/\\]temp[/\\].*',      # 临时目录
            r'.*[/\\]tmp[/\\].*',       # 临时目录
            r'.*[/\\]cache[/\\].*',     # 缓存目录
            r'.*[/\\]副本[/\\].*',      # 包含"副本"的目录
            r'.*[/\\]复制[/\\].*',      # 包含"复制"的目录
            r'.*[/\\]copy[/\\].*',      # 包含"copy"的目录
            r'.*[/\\]下载[/\\].*',      # 下载目录
            r'.*[/\\]download[/\\].*',  # 下载目录
        ],

        # 是否考虑文件修改时间（True: 保留较新的文件）
        'prefer_newer_files': True,

        # 是否考虑目录深度（True: 保留目录层级较浅的文件）
        'prefer_shallow_directories': True,

        # 是否显示选择理由
        'show_selection_reason': True,
    },
}

# 设置日志
def setup_logging():
    log_file = CONFIG['log_file']
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('duplicate_finder')

# 哈希缓存
_hash_cache = {}
_hash_cache_loaded = False

def load_hash_cache():
    """加载哈希缓存"""
    global _hash_cache, _hash_cache_loaded
    if _hash_cache_loaded:
        return

    cache_file = CONFIG['performance']['hash_cache_file']
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                _hash_cache = json.load(f)
        except Exception:
            _hash_cache = {}
    _hash_cache_loaded = True

def save_hash_cache():
    """保存哈希缓存"""
    if not CONFIG['performance']['cache_hashes']:
        return

    cache_file = CONFIG['performance']['hash_cache_file']
    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(_hash_cache, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.getLogger('duplicate_finder').warning(f"保存哈希缓存失败: {e}")

def get_file_hash(file_path, block_size=65536):
    """获取文件的哈希值，支持缓存"""
    try:
        # 获取文件信息用于缓存键
        stat = os.stat(file_path)
        file_size = stat.st_size
        file_mtime = stat.st_mtime

        # 创建缓存键
        cache_key = f"{file_path}:{file_size}:{file_mtime}"

        # 检查缓存
        if CONFIG['performance']['cache_hashes']:
            load_hash_cache()
            if cache_key in _hash_cache:
                return _hash_cache[cache_key]

        # 计算哈希值
        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            buf = f.read(block_size)
            while len(buf) > 0:
                hasher.update(buf)
                buf = f.read(block_size)

        hash_value = hasher.hexdigest()

        # 保存到缓存
        if CONFIG['performance']['cache_hashes']:
            _hash_cache[cache_key] = hash_value

        return hash_value
    except Exception as e:
        return None

# 智能选择要保留的文件
def smart_file_selection(path1, path2):
    """
    智能选择要保留的文件，返回 (original_file, duplicate_file, reason)
    优先级顺序：
    1. 文件名模式判断（最高优先级）
    2. 目录优先级
    3. 文件修改时间
    4. 目录深度
    5. 路径长度
    """
    strategy = CONFIG['file_selection_strategy']

    # 获取文件名（不含路径）
    name1 = os.path.basename(path1)
    name2 = os.path.basename(path2)

    # 0. 最高优先级：文件名模式判断
    # 检查是否有明确的副本文件名模式
    is_dup_1_2, conf_1_2, type_1_2 = is_duplicate_filename(name1, name2)  # name2是name1的副本
    is_dup_2_1, conf_2_1, type_2_1 = is_duplicate_filename(name2, name1)  # name1是name2的副本

    if is_dup_1_2 and conf_1_2 == 'high':  # 高置信度的副本模式
        reason = f"文件名模式判断(高置信度): '{name2}' 是 '{name1}' 的副本"
        return path1, path2, reason
    elif is_dup_2_1 and conf_2_1 == 'high':  # 高置信度的副本模式
        reason = f"文件名模式判断(高置信度): '{name1}' 是 '{name2}' 的副本"
        return path2, path1, reason

    # 1. 检查优先保留的目录模式
    for i, pattern in enumerate(strategy['priority_directories']):
        path1_match = re.search(pattern, path1, re.IGNORECASE)
        path2_match = re.search(pattern, path2, re.IGNORECASE)

        if path1_match and not path2_match:
            reason = f"路径1匹配优先保留目录模式: {pattern}"
            return path1, path2, reason
        elif path2_match and not path1_match:
            reason = f"路径2匹配优先保留目录模式: {pattern}"
            return path2, path1, reason

    # 2. 检查优先删除的目录模式
    for pattern in strategy['delete_priority_directories']:
        path1_match = re.search(pattern, path1, re.IGNORECASE)
        path2_match = re.search(pattern, path2, re.IGNORECASE)

        if path1_match and not path2_match:
            reason = f"路径1匹配优先删除目录模式: {pattern}"
            return path2, path1, reason
        elif path2_match and not path1_match:
            reason = f"路径2匹配优先删除目录模式: {pattern}"
            return path1, path2, reason

    # 2.5. 中等置信度的文件名模式判断（在目录优先级之后）
    if is_dup_1_2 and conf_1_2 == 'medium':  # 中等置信度的副本模式
        reason = f"文件名模式判断(中等置信度): '{name2}' 可能是 '{name1}' 的副本"
        return path1, path2, reason
    elif is_dup_2_1 and conf_2_1 == 'medium':  # 中等置信度的副本模式
        reason = f"文件名模式判断(中等置信度): '{name1}' 可能是 '{name2}' 的副本"
        return path2, path1, reason

    # 3. 考虑文件修改时间
    if strategy['prefer_newer_files']:
        try:
            mtime1 = os.path.getmtime(path1)
            mtime2 = os.path.getmtime(path2)

            if abs(mtime1 - mtime2) > 60:  # 相差超过1分钟才考虑
                if mtime1 > mtime2:
                    reason = f"路径1文件更新 (修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime1))})"
                    return path1, path2, reason
                else:
                    reason = f"路径2文件更新 (修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime2))})"
                    return path2, path1, reason
        except Exception:
            pass

    # 4. 考虑目录深度
    if strategy['prefer_shallow_directories']:
        depth1 = path1.count(os.sep)
        depth2 = path2.count(os.sep)

        if depth1 != depth2:
            if depth1 < depth2:
                reason = f"路径1目录层级较浅 (深度: {depth1} vs {depth2})"
                return path1, path2, reason
            else:
                reason = f"路径2目录层级较浅 (深度: {depth2} vs {depth1})"
                return path2, path1, reason

    # 5. 最后考虑路径长度（作为最后的判断标准）
    if len(path1) <= len(path2):
        reason = f"路径1较短 (长度: {len(path1)} vs {len(path2)})"
        return path1, path2, reason
    else:
        reason = f"路径2较短 (长度: {len(path2)} vs {len(path1)})"
        return path2, path1, reason

# 检查文件名是否是原始文件的副本
def is_duplicate_filename(original, potential_duplicate):
    """
    检查文件名是否是原始文件的副本
    返回: (is_duplicate, confidence, pattern_type)
    - is_duplicate: 是否是副本
    - confidence: 置信度 ('high' | 'medium' | 'low')
    - pattern_type: 匹配的模式类型 ('strict' | 'loose')
    """
    # 获取文件名和扩展名
    original_name, original_ext = os.path.splitext(original)
    dup_name, dup_ext = os.path.splitext(potential_duplicate)

    # 扩展名必须相同
    if original_ext.lower() != dup_ext.lower():
        return False, 'low', None

    # 检查是否原始文件名是潜在副本的前缀
    if not dup_name.startswith(original_name):
        return False, 'low', None

    # 检查后缀是否匹配任何重复模式
    suffix = dup_name[len(original_name):]
    if not suffix:
        return False, 'low', None

    patterns_config = CONFIG['duplicate_patterns']

    # 首先检查严格模式
    for pattern in patterns_config['strict_patterns']:
        if re.search(pattern, suffix, re.IGNORECASE):
            return True, 'high', 'strict'

    # 如果启用了宽松模式，检查宽松模式
    if patterns_config['enable_loose_mode']:
        for pattern in patterns_config['loose_patterns']:
            if re.search(pattern, suffix, re.IGNORECASE):
                confidence = 'medium' if patterns_config['loose_mode_require_hash'] else 'low'
                return True, confidence, 'loose'

    return False, 'low', None

# 兼容性函数：保持原有接口
def is_duplicate_filename_simple(original, potential_duplicate):
    """简化版本，只返回布尔值，保持向后兼容"""
    is_dup, confidence, pattern_type = is_duplicate_filename(original, potential_duplicate)
    return is_dup

# 文件过滤函数
def should_process_file(file_path, file_size):
    """判断文件是否应该被处理"""
    perf_config = CONFIG['performance']

    # 检查文件大小
    if file_size < perf_config['min_file_size']:
        return False

    # 检查文件扩展名
    file_ext = os.path.splitext(file_path)[1].lower()

    # 检查排除的扩展名
    if file_ext in perf_config['exclude_extensions']:
        return False

    # 检查包含的扩展名（如果设置了）
    if perf_config['file_extensions'] and file_ext not in perf_config['file_extensions']:
        return False

    return True

# 计算目录中的文件数量（用于进度条）
def count_files(directory):
    file_count = 0
    dirs_to_scan = [directory]
    scanned_dirs = 0
    total_dirs = 1  # 初始值为1，会在扫描过程中更新

    # 创建一个进度条来显示目录扫描进度
    pbar = tqdm(total=100, desc=f"统计文件数量 ({os.path.basename(directory) or directory})", unit="%")
    last_update = 0

    while dirs_to_scan:
        try:
            current_dir = dirs_to_scan.pop(0)
            try:
                with os.scandir(current_dir) as entries:
                    for entry in entries:
                        if entry.is_dir():
                            dirs_to_scan.append(entry.path)
                            total_dirs += 1
                        elif entry.is_file():
                            file_count += 1
            except PermissionError:
                continue  # 跳过无权限访问的目录
            except Exception as e:
                continue  # 跳过其他错误

            scanned_dirs += 1
            # 更新进度条，每秒最多更新10次以避免过多I/O
            current_time = time.time()
            if current_time - last_update > 0.1:
                progress = min(100, int(scanned_dirs / total_dirs * 100))
                pbar.n = progress
                pbar.refresh()
                last_update = current_time
        except Exception as e:
            pass  # 忽略错误继续扫描

    pbar.n = 100
    pbar.refresh()
    pbar.close()
    return file_count

# 收集目录中的所有文件
def collect_files(directory, total_files=None):
    files_info = []
    processed_files = 0

    # 创建进度条
    pbar = tqdm(total=total_files, desc=f"收集文件信息 ({os.path.basename(directory) or directory})", unit="文件")

    for root, _, files in os.walk(directory):
        for file in files:
            try:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                file_name = os.path.basename(file_path)
                folder = os.path.dirname(file_path)

                files_info.append((file_path, file_size, file_name, folder))

                processed_files += 1
                pbar.update(1)
            except Exception:
                # 忽略无法访问的文件
                pass

    # 确保进度条显示100%
    if total_files and processed_files < total_files:
        pbar.n = total_files
        pbar.refresh()

    pbar.close()
    return files_info

# 扫描目录并查找重复文件
def find_duplicates():
    logger = setup_logging()
    logger.info(f"开始查找重复文件，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"跨文件夹对比: {'启用' if CONFIG['cross_folder_comparison'] else '禁用'}")

    # 存储所有文件的信息
    all_files = {}  # 格式: {size: {name: [paths]}}
    duplicate_groups = []  # 存储找到的重复文件组

    # 扫描所有目录并收集文件信息
    logger.info("正在扫描目录并收集文件信息...")
    total_dirs = len(CONFIG['scan_directories'])

    for dir_idx, directory in enumerate(CONFIG['scan_directories']):
        if not os.path.exists(directory):
            logger.warning(f"目录不存在: {directory}")
            continue

        logger.info(f"扫描目录 ({dir_idx+1}/{total_dirs}): {directory}")

        # 先统计文件数量，用于进度条
        file_count = count_files(directory)
        logger.info(f"目录 {directory} 中共有 {file_count} 个文件")

        # 收集文件信息
        files_info = collect_files(directory, file_count)

        # 处理收集到的文件信息
        processed_files = 0
        skipped_files = 0

        for file_path, file_size, file_name, folder in files_info:
            try:
                # 应用文件过滤
                if not should_process_file(file_path, file_size):
                    skipped_files += 1
                    continue

                processed_files += 1

                if file_size not in all_files:
                    all_files[file_size] = {}

                if CONFIG['cross_folder_comparison']:
                    # 跨文件夹模式：所有文件放在一起比较
                    if file_name not in all_files[file_size]:
                        all_files[file_size][file_name] = []
                    all_files[file_size][file_name].append(file_path)
                else:
                    # 非跨文件夹模式：按目录分组
                    if folder not in all_files[file_size]:
                        all_files[file_size][folder] = {}
                    if file_name not in all_files[file_size][folder]:
                        all_files[file_size][folder][file_name] = []
                    all_files[file_size][folder][file_name].append(file_path)
            except Exception as e:
                logger.error(f"处理文件时出错: {file_path}, 错误: {str(e)}")

        logger.info(f"文件过滤结果: 处理 {processed_files} 个文件，跳过 {skipped_files} 个文件")

    # 查找重复文件
    logger.info("正在查找重复文件...")

    if CONFIG['debug_mode']:
        if CONFIG['cross_folder_comparison']:
            total_files_by_size = sum(len(paths) for names in all_files.values() for paths in names.values())
        else:
            total_files_by_size = sum(len(paths) for folders in all_files.values() for files in folders.values() for paths in files.values())
        logger.info(f"按文件大小分组后，共有 {len(all_files)} 个不同大小的文件组，总计 {total_files_by_size} 个文件")

    if CONFIG['cross_folder_comparison']:
        # 跨文件夹模式
        sizes_with_duplicates = [size for size, names in all_files.items() if len(names) > 1]

        if CONFIG['debug_mode']:
            logger.info(f"跨文件夹模式：找到 {len(sizes_with_duplicates)} 个可能包含重复文件的大小组")

        for size in tqdm(sizes_with_duplicates, desc="分析可能的重复文件", unit="组"):
            # 查找同一大小下的所有文件
            size_files = all_files[size]

            # 对每个文件名进行检查
            file_names = list(size_files.keys())

            if CONFIG['debug_mode'] and len(file_names) > 1:
                logger.info(f"检查大小为 {size} 字节的文件组，包含 {len(file_names)} 个不同文件名")
                for name in file_names:
                    logger.info(f"  文件名: {name}, 路径数: {len(size_files[name])}")

            # 首先处理相同文件名在不同目录的情况
            for file_name, paths in size_files.items():
                if len(paths) > 1:  # 相同文件名有多个路径
                    if CONFIG['debug_mode']:
                        logger.info(f"发现相同文件名在多个位置: '{file_name}' ({len(paths)} 个路径)")

                    # 检查每对路径是否在不同目录
                    for i in range(len(paths)):
                        for j in range(i+1, len(paths)):
                            path1 = paths[i]
                            path2 = paths[j]

                            dir1 = os.path.dirname(path1)
                            dir2 = os.path.dirname(path2)

                            if dir1 != dir2:  # 在不同目录中
                                if CONFIG['debug_mode']:
                                    logger.info(f"相同文件名跨文件夹: '{path1}' vs '{path2}'")
                                    logger.info(f"  目录1: {dir1}")
                                    logger.info(f"  目录2: {dir2}")

                                # 使用智能选择策略
                                original, duplicate, reason = smart_file_selection(path1, path2)
                                duplicate_groups.append((original, duplicate))

                                if CONFIG['debug_mode'] or CONFIG['file_selection_strategy']['show_selection_reason']:
                                    logger.info(f"添加重复文件组(相同名称): 原始='{original}', 副本='{duplicate}'")
                                    logger.info(f"  选择理由: {reason}")

            # 然后处理不同文件名之间的重复模式
            for i in range(len(file_names)):
                for j in range(i+1, len(file_names)):
                    name1 = file_names[i]
                    name2 = file_names[j]

                    paths1 = size_files[name1]
                    paths2 = size_files[name2]

                    # 检查是否是重复文件名模式
                    is_dup_1_2, conf_1_2, type_1_2 = is_duplicate_filename(name1, name2)  # name2是name1的副本
                    is_dup_2_1, conf_2_1, type_2_1 = is_duplicate_filename(name2, name1)  # name1是name2的副本

                    if CONFIG['debug_mode'] and (is_dup_1_2 or is_dup_2_1):
                        logger.info(f"发现潜在重复文件名: '{name1}' vs '{name2}'")
                        logger.info(f"  is_dup_1_2: {is_dup_1_2} (置信度: {conf_1_2}, 类型: {type_1_2})")
                        logger.info(f"  is_dup_2_1: {is_dup_2_1} (置信度: {conf_2_1}, 类型: {type_2_1})")

                    # 根据置信度和配置决定是否需要哈希验证
                    need_hash_verification = False
                    if is_dup_1_2 and type_1_2 == 'loose' and CONFIG['duplicate_patterns']['loose_mode_require_hash']:
                        need_hash_verification = True
                    elif is_dup_2_1 and type_2_1 == 'loose' and CONFIG['duplicate_patterns']['loose_mode_require_hash']:
                        need_hash_verification = True

                    if is_dup_1_2 or is_dup_2_1:
                        # 对于每个路径组合进行检查
                        for path1 in paths1:
                            for path2 in paths2:
                                # 确保不是同一个文件
                                if path1 != path2:
                                    # 确保在不同文件夹中（跨文件夹对比的核心）
                                    dir1 = os.path.dirname(path1)
                                    dir2 = os.path.dirname(path2)

                                    if CONFIG['debug_mode']:
                                        logger.info(f"比较路径: '{path1}' vs '{path2}'")
                                        logger.info(f"  目录1: {dir1}")
                                        logger.info(f"  目录2: {dir2}")
                                        logger.info(f"  不同目录: {dir1 != dir2}")

                                    if dir1 != dir2:
                                        # 根据配置和置信度决定是否需要哈希验证
                                        should_verify_hash = CONFIG['verify_with_hash'] or need_hash_verification

                                        if should_verify_hash:
                                            hash1 = get_file_hash(path1)
                                            hash2 = get_file_hash(path2)
                                            if hash1 and hash2 and hash1 == hash2:
                                                if is_dup_1_2:
                                                    original, duplicate, reason = smart_file_selection(path1, path2)
                                                    if original == path1:
                                                        duplicate_groups.append((path1, path2))  # path2是副本
                                                        reason = f"文件名模式判断({type_1_2}模式,{conf_1_2}置信度): {name2} 是 {name1} 的副本"
                                                    else:
                                                        duplicate_groups.append((original, duplicate))
                                                        reason = f"智能选择覆盖文件名模式: {reason}"

                                                    if CONFIG['debug_mode'] or CONFIG['file_selection_strategy']['show_selection_reason']:
                                                        logger.info(f"添加重复文件组(哈希验证): 原始='{original}', 副本='{duplicate}'")
                                                        logger.info(f"  选择理由: {reason}")
                                                elif is_dup_2_1:
                                                    original, duplicate, reason = smart_file_selection(path1, path2)
                                                    if original == path2:
                                                        duplicate_groups.append((path2, path1))  # path1是副本
                                                        reason = f"文件名模式判断({type_2_1}模式,{conf_2_1}置信度): {name1} 是 {name2} 的副本"
                                                    else:
                                                        duplicate_groups.append((original, duplicate))
                                                        reason = f"智能选择覆盖文件名模式: {reason}"

                                                    if CONFIG['debug_mode'] or CONFIG['file_selection_strategy']['show_selection_reason']:
                                                        logger.info(f"添加重复文件组(哈希验证): 原始='{original}', 副本='{duplicate}'")
                                                        logger.info(f"  选择理由: {reason}")
                                            elif need_hash_verification:
                                                # 宽松模式要求哈希验证但验证失败，跳过
                                                if CONFIG['debug_mode']:
                                                    logger.info(f"宽松模式哈希验证失败，跳过: '{name1}' vs '{name2}'")
                                        else:
                                            if is_dup_1_2:
                                                # name2是name1的副本，但也要考虑其他因素
                                                original, duplicate, reason = smart_file_selection(path1, path2)
                                                # 如果智能选择的结果与文件名模式冲突，优先考虑文件名模式
                                                if original == path1:
                                                    duplicate_groups.append((path1, path2))  # path2是副本
                                                    reason = f"文件名模式判断: {name2} 是 {name1} 的副本"
                                                else:
                                                    # 智能选择建议保留path2，但文件名模式显示path2是副本
                                                    # 这种情况下，显示冲突信息让用户决定
                                                    duplicate_groups.append((path1, path2))  # 仍然按文件名模式
                                                    reason = f"文件名模式判断: {name2} 是 {name1} 的副本 (与智能选择冲突: {reason})"

                                                if CONFIG['debug_mode'] or CONFIG['file_selection_strategy']['show_selection_reason']:
                                                    logger.info(f"添加重复文件组: 原始='{path1}', 副本='{path2}'")
                                                    logger.info(f"  选择理由: {reason}")
                                            elif is_dup_2_1:
                                                # name1是name2的副本
                                                original, duplicate, reason = smart_file_selection(path1, path2)
                                                if original == path2:
                                                    duplicate_groups.append((path2, path1))  # path1是副本
                                                    reason = f"文件名模式判断: {name1} 是 {name2} 的副本"
                                                else:
                                                    duplicate_groups.append((path2, path1))  # 仍然按文件名模式
                                                    reason = f"文件名模式判断: {name1} 是 {name2} 的副本 (与智能选择冲突: {reason})"

                                                if CONFIG['debug_mode'] or CONFIG['file_selection_strategy']['show_selection_reason']:
                                                    logger.info(f"添加重复文件组: 原始='{path2}', 副本='{path1}'")
                                                    logger.info(f"  选择理由: {reason}")
    else:
        # 非跨文件夹模式
        for size, folders in tqdm(all_files.items(), desc="分析文件夹", unit="大小"):
            for folder, files in folders.items():
                # 查找同一文件夹下的重复文件
                file_names = list(files.keys())
                for i in range(len(file_names)):
                    for j in range(i+1, len(file_names)):
                        name1 = file_names[i]
                        name2 = file_names[j]

                        paths1 = files[name1]
                        paths2 = files[name2]

                        # 检查是否是重复文件名模式
                        is_dup_1_2, conf_1_2, type_1_2 = is_duplicate_filename(name1, name2)  # name2是name1的副本
                        is_dup_2_1, conf_2_1, type_2_1 = is_duplicate_filename(name2, name1)  # name1是name2的副本

                        if is_dup_1_2 or is_dup_2_1:
                            # 对于每个路径组合进行检查
                            for path1 in paths1:
                                for path2 in paths2:
                                    # 确保不是同一个文件
                                    if path1 != path2:
                                        # 使用智能选择策略
                                        original, duplicate, reason = smart_file_selection(path1, path2)
                                        duplicate_groups.append((original, duplicate))

                                        if CONFIG['debug_mode'] or CONFIG['file_selection_strategy']['show_selection_reason']:
                                            logger.info(f"添加重复文件组(同文件夹): 原始='{original}', 副本='{duplicate}'")
                                            logger.info(f"  选择理由: {reason}")

    # 处理重复文件
    if duplicate_groups:
        logger.info(f"找到 {len(duplicate_groups)} 组重复文件")
        process_duplicates(duplicate_groups)
    else:
        logger.info("未找到重复文件")

    logger.info(f"查找重复文件完成，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# 判断是否应该自动删除
def should_auto_delete(original, duplicate):
    """根据智能规则判断是否应该自动删除"""
    _, _, reason = smart_file_selection(original, duplicate)

    for pattern in CONFIG['delete_mode']['smart_auto_patterns']:
        if pattern in reason:
            return True, reason
    return False, reason

# 显示文件详细信息
def show_file_info(file_path, label):
    try:
        stat = os.stat(file_path)
        size = stat.st_size
        mtime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))
        print(f"  {label}: {file_path}")
        print(f"    大小: {size:,} 字节")
        print(f"    修改时间: {mtime}")
        print(f"    目录: {os.path.dirname(file_path)}")
    except Exception as e:
        print(f"  {label}: {file_path} (无法获取详细信息: {e})")

# 处理重复文件
def process_duplicates(duplicate_groups):
    logger = logging.getLogger('duplicate_finder')
    delete_mode = CONFIG['delete_mode']['mode']

    print(f"\n删除模式: {delete_mode}")
    if delete_mode == 'auto':
        print("将自动删除所有重复文件")
    elif delete_mode == 'confirm_each':
        print("将逐个确认每个重复文件")
    elif delete_mode == 'batch_confirm':
        batch_size = CONFIG['delete_mode']['batch_size']
        print(f"将分批确认，每批 {batch_size} 个文件")
    elif delete_mode == 'smart_auto':
        print("将智能自动删除明确的重复文件，模糊情况下询问")

    deleted_count = 0
    skipped_count = 0

    if delete_mode == 'batch_confirm':
        # 批量确认模式
        batch_size = CONFIG['delete_mode']['batch_size']
        for batch_start in range(0, len(duplicate_groups), batch_size):
            batch_end = min(batch_start + batch_size, len(duplicate_groups))
            batch = duplicate_groups[batch_start:batch_end]

            print(f"\n" + "="*60)
            print(f"批次 {batch_start//batch_size + 1}: 显示第 {batch_start+1}-{batch_end} 个重复文件组")
            print("="*60)

            # 显示这一批的所有文件
            for i, (original, duplicate) in enumerate(batch):
                actual_index = batch_start + i + 1
                print(f"\n{actual_index}. 重复文件组:")
                show_file_info(original, "保留文件")
                show_file_info(duplicate, "删除文件")

                # 显示选择理由
                if CONFIG['file_selection_strategy']['show_selection_reason']:
                    _, _, reason = smart_file_selection(original, duplicate)
                    print(f"  选择理由: {reason}")

            # 批量确认
            print(f"\n批量操作选项:")
            print(f"  a = 删除这批所有文件")
            print(f"  s = 跳过这批所有文件")
            print(f"  i = 逐个确认这批文件")
            print(f"  q = 退出处理")

            batch_choice = input(f"请选择操作 (a/s/i/q): ").strip().lower()

            if batch_choice == 'q':
                print("用户选择退出")
                break
            elif batch_choice == 's':
                print(f"跳过批次 {batch_start//batch_size + 1} 的所有文件")
                skipped_count += len(batch)
                continue
            elif batch_choice == 'a':
                # 删除这批所有文件
                for i, (original, duplicate) in enumerate(batch):
                    try:
                        os.remove(duplicate)
                        deleted_count += 1
                        logger.info(f"已删除重复文件: {duplicate}")
                    except Exception as e:
                        logger.error(f"删除文件时出错: {duplicate}, 错误: {str(e)}")
                print(f"批次 {batch_start//batch_size + 1} 处理完成")
            elif batch_choice == 'i':
                # 逐个确认这批文件
                for i, (original, duplicate) in enumerate(batch):
                    actual_index = batch_start + i + 1
                    print(f"\n确认删除第 {actual_index} 个重复文件:")
                    print(f"  删除: {duplicate}")
                    confirm = input("确认删除? (y/n/s=跳过剩余): ").strip().lower()

                    if confirm == 's':
                        print("跳过剩余文件")
                        skipped_count += len(batch) - i
                        break
                    elif confirm == 'y':
                        try:
                            os.remove(duplicate)
                            deleted_count += 1
                            logger.info(f"已删除重复文件: {duplicate}")
                        except Exception as e:
                            logger.error(f"删除文件时出错: {duplicate}, 错误: {str(e)}")
                    else:
                        skipped_count += 1
                        logger.info(f"用户选择不删除: {duplicate}")

    elif delete_mode == 'smart_auto':
        # 智能自动模式
        for i, (original, duplicate) in enumerate(tqdm(duplicate_groups, desc="智能处理重复文件", unit="组")):
            auto_delete, reason = should_auto_delete(original, duplicate)

            if auto_delete:
                try:
                    os.remove(duplicate)
                    deleted_count += 1
                    logger.info(f"自动删除重复文件: {duplicate} (理由: {reason})")
                except Exception as e:
                    logger.error(f"删除文件时出错: {duplicate}, 错误: {str(e)}")
            else:
                # 需要用户确认
                print(f"\n需要确认的重复文件 ({i+1}/{len(duplicate_groups)}):")
                show_file_info(original, "保留文件")
                show_file_info(duplicate, "删除文件")
                print(f"  选择理由: {reason}")

                confirm = input("确认删除重复文件? (y/n/s=跳过剩余): ").strip().lower()
                if confirm == 's':
                    print("跳过剩余需要确认的文件")
                    skipped_count += len(duplicate_groups) - i
                    break
                elif confirm == 'y':
                    try:
                        os.remove(duplicate)
                        deleted_count += 1
                        logger.info(f"用户确认删除重复文件: {duplicate}")
                    except Exception as e:
                        logger.error(f"删除文件时出错: {duplicate}, 错误: {str(e)}")
                else:
                    skipped_count += 1
                    logger.info(f"用户选择不删除: {duplicate}")

    elif delete_mode == 'auto':
        # 自动删除模式
        for original, duplicate in tqdm(duplicate_groups, desc="自动删除重复文件", unit="组"):
            try:
                os.remove(duplicate)
                deleted_count += 1
                logger.info(f"自动删除重复文件: {duplicate}")
            except Exception as e:
                logger.error(f"删除文件时出错: {duplicate}, 错误: {str(e)}")

    else:  # confirm_each
        # 逐个确认模式
        for i, (original, duplicate) in enumerate(duplicate_groups):
            print(f"\n重复文件 ({i+1}/{len(duplicate_groups)}):")
            show_file_info(original, "保留文件")
            show_file_info(duplicate, "删除文件")

            if CONFIG['file_selection_strategy']['show_selection_reason']:
                _, _, reason = smart_file_selection(original, duplicate)
                print(f"  选择理由: {reason}")

            confirm = input("确认删除重复文件? (y/n/s=跳过剩余/q=退出): ").strip().lower()
            if confirm == 'q':
                print("用户选择退出")
                break
            elif confirm == 's':
                print("跳过剩余文件")
                skipped_count += len(duplicate_groups) - i
                break
            elif confirm == 'y':
                try:
                    os.remove(duplicate)
                    deleted_count += 1
                    logger.info(f"用户确认删除重复文件: {duplicate}")
                except Exception as e:
                    logger.error(f"删除文件时出错: {duplicate}, 错误: {str(e)}")
            else:
                skipped_count += 1
                logger.info(f"用户选择不删除: {duplicate}")

    # 显示处理结果
    print(f"\n" + "="*50)
    print(f"处理完成!")
    print(f"  已删除文件: {deleted_count} 个")
    print(f"  跳过文件: {skipped_count} 个")
    print(f"  总计处理: {len(duplicate_groups)} 组重复文件")
    print("="*50)

if __name__ == "__main__":
    try:
        find_duplicates()
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        logging.getLogger('duplicate_finder').info("操作被用户中断")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        logging.getLogger('duplicate_finder').error(f"发生错误: {str(e)}")
