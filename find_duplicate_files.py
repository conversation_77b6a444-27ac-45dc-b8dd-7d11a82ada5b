#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import hashlib
import logging
import time
from datetime import datetime
from tqdm import tqdm
import shutil

# 配置项
CONFIG = {
    # 是否跨文件夹对比
    'cross_folder_comparison': True,

    # 要扫描的目录列表
    'scan_directories': [
        #'Z:\Binance', #zip路径
        #'W:\\', #图书馆
        #'H:\\',  #csv路径
        #'D:\\360Downloads', #baidudownload
        #'X:\\【资料库】柚子',
        #'U:\\',  #cpa
        #'X:\【研究】nynx', #nynx
        #'X:\\',
        'Z:\饼圈合集',
        #可以添加多个目录W:\【azw3】, W:\【mobi】, W:\【epub】, W:\【txt】,W:\【pdf】,W:\【chm】
    ],

    # 自动删除模式，如果为False，则在删除前请求确认
    'auto_delete': True,

    # 日志文件路径
    'log_file': 'duplicate_files_log.txt',

    # 重复文件名模式（正则表达式）
    'duplicate_patterns': [
        r'\(\d+\)$',  # 匹配 "(1)", "(2)" 等
        r'副本$',
        r'复制$',
        r'copy$',
        r'_\d+$',  # 匹配 "_1", "_2" 等
    ],

    # 是否计算文件哈希值进行额外验证（会降低速度但提高准确性）
    'verify_with_hash': False,
}

# 设置日志
def setup_logging():
    log_file = CONFIG['log_file']
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('duplicate_finder')

# 获取文件的哈希值
def get_file_hash(file_path, block_size=65536):
    hasher = hashlib.md5()
    try:
        with open(file_path, 'rb') as f:
            buf = f.read(block_size)
            while len(buf) > 0:
                hasher.update(buf)
                buf = f.read(block_size)
        return hasher.hexdigest()
    except Exception as e:
        return None

# 检查文件名是否是原始文件的副本
def is_duplicate_filename(original, potential_duplicate):
    # 获取文件名和扩展名
    original_name, original_ext = os.path.splitext(original)
    dup_name, dup_ext = os.path.splitext(potential_duplicate)

    # 扩展名必须相同
    if original_ext.lower() != dup_ext.lower():
        return False

    # 检查是否原始文件名是潜在副本的前缀
    if not dup_name.startswith(original_name):
        return False

    # 检查后缀是否匹配任何重复模式
    suffix = dup_name[len(original_name):]
    if not suffix:
        return False

    for pattern in CONFIG['duplicate_patterns']:
        if re.search(pattern, suffix):
            return True

    return False

# 计算目录中的文件数量（用于进度条）
def count_files(directory):
    file_count = 0
    dirs_to_scan = [directory]
    scanned_dirs = 0
    total_dirs = 1  # 初始值为1，会在扫描过程中更新

    # 创建一个进度条来显示目录扫描进度
    pbar = tqdm(total=100, desc=f"统计文件数量 ({os.path.basename(directory) or directory})", unit="%")
    last_update = 0

    while dirs_to_scan:
        try:
            current_dir = dirs_to_scan.pop(0)
            try:
                with os.scandir(current_dir) as entries:
                    for entry in entries:
                        if entry.is_dir():
                            dirs_to_scan.append(entry.path)
                            total_dirs += 1
                        elif entry.is_file():
                            file_count += 1
            except PermissionError:
                continue  # 跳过无权限访问的目录
            except Exception as e:
                continue  # 跳过其他错误

            scanned_dirs += 1
            # 更新进度条，每秒最多更新10次以避免过多I/O
            current_time = time.time()
            if current_time - last_update > 0.1:
                progress = min(100, int(scanned_dirs / total_dirs * 100))
                pbar.n = progress
                pbar.refresh()
                last_update = current_time
        except Exception as e:
            pass  # 忽略错误继续扫描

    pbar.n = 100
    pbar.refresh()
    pbar.close()
    return file_count

# 收集目录中的所有文件
def collect_files(directory, total_files=None):
    files_info = []
    processed_files = 0

    # 创建进度条
    pbar = tqdm(total=total_files, desc=f"收集文件信息 ({os.path.basename(directory) or directory})", unit="文件")

    for root, _, files in os.walk(directory):
        for file in files:
            try:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                file_name = os.path.basename(file_path)
                folder = os.path.dirname(file_path)

                files_info.append((file_path, file_size, file_name, folder))

                processed_files += 1
                pbar.update(1)
            except Exception:
                # 忽略无法访问的文件
                pass

    # 确保进度条显示100%
    if total_files and processed_files < total_files:
        pbar.n = total_files
        pbar.refresh()

    pbar.close()
    return files_info

# 扫描目录并查找重复文件
def find_duplicates():
    logger = setup_logging()
    logger.info(f"开始查找重复文件，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"跨文件夹对比: {'启用' if CONFIG['cross_folder_comparison'] else '禁用'}")

    # 存储所有文件的信息
    all_files = {}  # 格式: {size: {name: [paths]}}
    duplicate_groups = []  # 存储找到的重复文件组

    # 扫描所有目录并收集文件信息
    logger.info("正在扫描目录并收集文件信息...")
    total_dirs = len(CONFIG['scan_directories'])

    for dir_idx, directory in enumerate(CONFIG['scan_directories']):
        if not os.path.exists(directory):
            logger.warning(f"目录不存在: {directory}")
            continue

        logger.info(f"扫描目录 ({dir_idx+1}/{total_dirs}): {directory}")

        # 先统计文件数量，用于进度条
        file_count = count_files(directory)
        logger.info(f"目录 {directory} 中共有 {file_count} 个文件")

        # 收集文件信息
        files_info = collect_files(directory, file_count)

        # 处理收集到的文件信息
        for file_path, file_size, file_name, folder in files_info:
            try:
                if file_size not in all_files:
                    all_files[file_size] = {}

                if CONFIG['cross_folder_comparison']:
                    # 跨文件夹模式：所有文件放在一起比较
                    if file_name not in all_files[file_size]:
                        all_files[file_size][file_name] = []
                    all_files[file_size][file_name].append(file_path)
                else:
                    # 非跨文件夹模式：按目录分组
                    if folder not in all_files[file_size]:
                        all_files[file_size][folder] = {}
                    if file_name not in all_files[file_size][folder]:
                        all_files[file_size][folder][file_name] = []
                    all_files[file_size][folder][file_name].append(file_path)
            except Exception as e:
                logger.error(f"处理文件时出错: {file_path}, 错误: {str(e)}")

    # 查找重复文件
    logger.info("正在查找重复文件...")

    if CONFIG['cross_folder_comparison']:
        # 跨文件夹模式
        sizes_with_duplicates = [size for size, names in all_files.items() if any(len(paths) > 1 for paths in names.values())]

        for size in tqdm(sizes_with_duplicates, desc="分析可能的重复文件", unit="组"):
            # 查找同一大小下的所有文件
            size_files = all_files[size]

            # 对每个文件名进行检查
            file_names = list(size_files.keys())
            for i in range(len(file_names)):
                for j in range(i+1, len(file_names)):
                    name1 = file_names[i]
                    name2 = file_names[j]

                    # 检查文件名是否相似
                    if is_duplicate_filename(name1, name2):
                        paths1 = size_files[name1]
                        paths2 = size_files[name2]

                        # 如果需要，通过哈希值进一步验证
                        if CONFIG['verify_with_hash']:
                            hash1 = get_file_hash(paths1[0])
                            hash2 = get_file_hash(paths2[0])
                            if hash1 and hash2 and hash1 == hash2:
                                duplicate_groups.append((paths1[0], paths2[0]))
                        else:
                            duplicate_groups.append((paths1[0], paths2[0]))
                    elif is_duplicate_filename(name2, name1):
                        paths1 = size_files[name1]
                        paths2 = size_files[name2]

                        # 如果需要，通过哈希值进一步验证
                        if CONFIG['verify_with_hash']:
                            hash1 = get_file_hash(paths1[0])
                            hash2 = get_file_hash(paths2[0])
                            if hash1 and hash2 and hash1 == hash2:
                                duplicate_groups.append((paths2[0], paths1[0]))
                        else:
                            duplicate_groups.append((paths2[0], paths1[0]))
    else:
        # 非跨文件夹模式
        for size, folders in tqdm(all_files.items(), desc="分析文件夹", unit="大小"):
            for folder, files in folders.items():
                # 查找同一文件夹下的重复文件
                file_names = list(files.keys())
                for i in range(len(file_names)):
                    for j in range(i+1, len(file_names)):
                        name1 = file_names[i]
                        name2 = file_names[j]

                        # 检查文件名是否相似
                        if is_duplicate_filename(name1, name2):
                            paths1 = files[name1]
                            paths2 = files[name2]

                            # 如果需要，通过哈希值进一步验证
                            if CONFIG['verify_with_hash']:
                                hash1 = get_file_hash(paths1[0])
                                hash2 = get_file_hash(paths2[0])
                                if hash1 and hash2 and hash1 == hash2:
                                    duplicate_groups.append((paths1[0], paths2[0]))
                            else:
                                duplicate_groups.append((paths1[0], paths2[0]))
                        elif is_duplicate_filename(name2, name1):
                            paths1 = files[name1]
                            paths2 = files[name2]

                            # 如果需要，通过哈希值进一步验证
                            if CONFIG['verify_with_hash']:
                                hash1 = get_file_hash(paths1[0])
                                hash2 = get_file_hash(paths2[0])
                                if hash1 and hash2 and hash1 == hash2:
                                    duplicate_groups.append((paths2[0], paths1[0]))
                            else:
                                duplicate_groups.append((paths2[0], paths1[0]))

    # 处理重复文件
    if duplicate_groups:
        logger.info(f"找到 {len(duplicate_groups)} 组重复文件")
        process_duplicates(duplicate_groups)
    else:
        logger.info("未找到重复文件")

    logger.info(f"查找重复文件完成，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# 处理重复文件
def process_duplicates(duplicate_groups):
    logger = logging.getLogger('duplicate_finder')

    # 按组处理重复文件
    for i, (original, duplicate) in enumerate(tqdm(duplicate_groups, desc="处理重复文件", unit="组")):
        try:
            original_name = os.path.basename(original)
            duplicate_name = os.path.basename(duplicate)
            original_dir = os.path.dirname(original)
            duplicate_dir = os.path.dirname(duplicate)

            logger.info(f"重复文件组 {i+1}/{len(duplicate_groups)}:")
            logger.info(f"  原始文件: {original}")
            logger.info(f"  重复文件: {duplicate}")

            # 确认是否删除
            if not CONFIG['auto_delete']:
                if CONFIG['cross_folder_comparison'] and original_dir != duplicate_dir:
                    print(f"\n跨文件夹重复文件 ({i+1}/{len(duplicate_groups)}):")
                    print(f"  原始文件: {original}")
                    print(f"  重复文件: {duplicate}")
                    confirm = input("确认删除重复文件? (y/n): ").strip().lower()
                    if confirm != 'y':
                        logger.info("  用户选择不删除")
                        continue
                else:
                    print(f"\n同文件夹重复文件 ({i+1}/{len(duplicate_groups)}):")
                    print(f"  原始文件: {original}")
                    print(f"  重复文件: {duplicate}")
                    confirm = input("确认删除重复文件? (y/n): ").strip().lower()
                    if confirm != 'y':
                        logger.info("  用户选择不删除")
                        continue

            # 删除重复文件
            os.remove(duplicate)
            logger.info(f"  已删除重复文件: {duplicate}")

        except Exception as e:
            logger.error(f"  删除文件时出错: {duplicate}, 错误: {str(e)}")

if __name__ == "__main__":
    try:
        find_duplicates()
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        logging.getLogger('duplicate_finder').info("操作被用户中断")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        logging.getLogger('duplicate_finder').error(f"发生错误: {str(e)}")
