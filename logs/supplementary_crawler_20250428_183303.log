2025-04-28 18:33:03,378 [INFO] 没有找到验证日志文件，使用最新的日志文件: logs\supplementary_crawler_20250428_183303.log
2025-04-28 18:33:03,382 [INFO] 从日志文件中提取到 3 个缺失的币对
2025-04-28 18:33:03,383 [INFO] 缺失币对示例: 28 18
2025-04-28 18:33:03,383 [INFO] 启动Chrome浏览器...
2025-04-28 18:33:06,876 [INFO] 成功加载现有结果文件，包含 126 个币对
2025-04-28 18:33:06,877 [INFO] 开始补充爬取 3 个带时间周期的缺失币对数据...
2025-04-28 18:33:06,878 [INFO] 处理币对 1/3: 28 18
2025-04-28 18:33:06,879 [INFO] 正在访问 28 的 klines 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/klines/28/18/
2025-04-28 18:33:17,840 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-04-28 18:33:17,845 [WARNING]   JavaScript方法失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=135.0.7049.115)
Stacktrace:
	GetHandleVerifier [0x00007FF6CE16EFA5+77893]
	GetHandleVerifier [0x00007FF6CE16F000+77984]
	(No symbol) [0x00007FF6CDF391BA]
	(No symbol) [0x00007FF6CDF24F15]
	(No symbol) [0x00007FF6CDF49F04]
	(No symbol) [0x00007FF6CDFBEACF]
	(No symbol) [0x00007FF6CDFDE972]
	(No symbol) [0x00007FF6CDFB6F03]
	(No symbol) [0x00007FF6CDF80328]
	(No symbol) [0x00007FF6CDF81093]
	GetHandleVerifier [0x00007FF6CE427B6D+2931725]
	GetHandleVerifier [0x00007FF6CE422132+2908626]
	GetHandleVerifier [0x00007FF6CE4400F3+3031443]
	GetHandleVerifier [0x00007FF6CE1891EA+184970]
	GetHandleVerifier [0x00007FF6CE19086F+215311]
	GetHandleVerifier [0x00007FF6CE176EC4+110436]
	GetHandleVerifier [0x00007FF6CE177072+110866]
	GetHandleVerifier [0x00007FF6CE15D479+5401]
	BaseThreadInitThunk [0x00007FFA4F80259D+29]
	RtlUserThreadStart [0x00007FFA51AAAF38+40]

2025-04-28 18:33:17,852 [INFO]   尝试使用页面源码方法获取数据文件...
2025-04-28 18:33:18,293 [WARNING]   页面源码方法失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF6CE16EFA5+77893]
	GetHandleVerifier [0x00007FF6CE16F000+77984]
	(No symbol) [0x00007FF6CDF38FEC]
	(No symbol) [0x00007FF6CDF7F44F]
	(No symbol) [0x00007FF6CDFB6FF2]
	(No symbol) [0x00007FF6CDFB1A02]
	(No symbol) [0x00007FF6CDFB0AC9]
	(No symbol) [0x00007FF6CDF05AB5]
	GetHandleVerifier [0x00007FF6CE427B6D+2931725]
	GetHandleVerifier [0x00007FF6CE422132+2908626]
	GetHandleVerifier [0x00007FF6CE4400F3+3031443]
	GetHandleVerifier [0x00007FF6CE1891EA+184970]
	GetHandleVerifier [0x00007FF6CE19086F+215311]
	(No symbol) [0x00007FF6CDF04B2C]
	GetHandleVerifier [0x00007FF6CE532518+4023736]
	BaseThreadInitThunk [0x00007FFA4F80259D+29]
	RtlUserThreadStart [0x00007FFA51AAAF38+40]

2025-04-28 18:33:18,299 [INFO]   尝试使用表格解析方法获取数据文件...
2025-04-28 18:33:18,302 [WARNING]   表格解析方法失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF6CE16EFA5+77893]
	GetHandleVerifier [0x00007FF6CE16F000+77984]
	(No symbol) [0x00007FF6CDF38FEC]
	(No symbol) [0x00007FF6CDF7F44F]
	(No symbol) [0x00007FF6CDFB6FF2]
	(No symbol) [0x00007FF6CDFB1A02]
	(No symbol) [0x00007FF6CDFB0AC9]
	(No symbol) [0x00007FF6CDF05AB5]
	GetHandleVerifier [0x00007FF6CE427B6D+2931725]
	GetHandleVerifier [0x00007FF6CE422132+2908626]
	GetHandleVerifier [0x00007FF6CE4400F3+3031443]
	GetHandleVerifier [0x00007FF6CE1891EA+184970]
	GetHandleVerifier [0x00007FF6CE19086F+215311]
	(No symbol) [0x00007FF6CDF04B2C]
	GetHandleVerifier [0x00007FF6CE532518+4023736]
	BaseThreadInitThunk [0x00007FFA4F80259D+29]
	RtlUserThreadStart [0x00007FFA51AAAF38+40]

2025-04-28 18:33:18,308 [INFO]   尝试使用XPath方法获取数据文件...
2025-04-28 18:33:18,310 [WARNING]   XPath方法失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF6CE16EFA5+77893]
	GetHandleVerifier [0x00007FF6CE16F000+77984]
	(No symbol) [0x00007FF6CDF38FEC]
	(No symbol) [0x00007FF6CDF7F44F]
	(No symbol) [0x00007FF6CDFB6FF2]
	(No symbol) [0x00007FF6CDFB1A02]
	(No symbol) [0x00007FF6CDFB0AC9]
	(No symbol) [0x00007FF6CDF05AB5]
	GetHandleVerifier [0x00007FF6CE427B6D+2931725]
	GetHandleVerifier [0x00007FF6CE422132+2908626]
	GetHandleVerifier [0x00007FF6CE4400F3+3031443]
	GetHandleVerifier [0x00007FF6CE1891EA+184970]
	GetHandleVerifier [0x00007FF6CE19086F+215311]
	(No symbol) [0x00007FF6CDF04B2C]
	GetHandleVerifier [0x00007FF6CE532518+4023736]
	BaseThreadInitThunk [0x00007FFA4F80259D+29]
	RtlUserThreadStart [0x00007FFA51AAAF38+40]

2025-04-28 18:33:18,315 [INFO]   尝试使用所有链接方法获取数据文件...
2025-04-28 18:33:18,318 [WARNING]   所有链接方法失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF6CE16EFA5+77893]
	GetHandleVerifier [0x00007FF6CE16F000+77984]
	(No symbol) [0x00007FF6CDF38FEC]
	(No symbol) [0x00007FF6CDF7F44F]
	(No symbol) [0x00007FF6CDFB6FF2]
	(No symbol) [0x00007FF6CDFB1A02]
	(No symbol) [0x00007FF6CDFB0AC9]
	(No symbol) [0x00007FF6CDF05AB5]
	GetHandleVerifier [0x00007FF6CE427B6D+2931725]
	GetHandleVerifier [0x00007FF6CE422132+2908626]
	GetHandleVerifier [0x00007FF6CE4400F3+3031443]
	GetHandleVerifier [0x00007FF6CE1891EA+184970]
	GetHandleVerifier [0x00007FF6CE19086F+215311]
	(No symbol) [0x00007FF6CDF04B2C]
	GetHandleVerifier [0x00007FF6CE532518+4023736]
	BaseThreadInitThunk [0x00007FFA4F80259D+29]
	RtlUserThreadStart [0x00007FFA51AAAF38+40]

2025-04-28 18:33:18,323 [WARNING]   使用所有方法都无法获取 28 的数据文件
2025-04-28 18:33:18,324 [WARNING] 跳过格式错误的币对数据: 04
2025-04-28 18:33:18,325 [WARNING] 跳过格式错误的币对数据: 2
2025-04-28 18:33:18,326 [INFO] 
补充爬取完成
2025-04-28 18:33:18,326 [INFO] 成功获取数据的币对: 0/3
2025-04-28 18:33:18,327 [WARNING] 未能获取数据的币对列表:
2025-04-28 18:33:18,328 [WARNING]   - 28 18
2025-04-28 18:33:18,335 [INFO] 结果已保存到: binance_links\futures_um_monthly_klines_all_links.txt
2025-04-28 18:33:18,335 [INFO] 
=== 汇总信息 ===
2025-04-28 18:33:18,336 [INFO] 市场类型: futures/um
2025-04-28 18:33:18,337 [INFO] 数据类型: klines
2025-04-28 18:33:18,337 [INFO] 时间周期: 1d
2025-04-28 18:33:18,338 [INFO] 数据频率: monthly
2025-04-28 18:33:18,339 [INFO] 处理的币对数: 3
2025-04-28 18:33:18,339 [INFO] 成功获取数据的币对数: 0
2025-04-28 18:33:18,340 [INFO] 结果文件: binance_links\futures_um_monthly_klines_all_links.txt
2025-04-28 18:33:20,373 [INFO] 浏览器已关闭
