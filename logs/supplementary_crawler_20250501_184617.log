2025-05-01 18:46:17,138 [INFO] 开始运行币安数据补充爬虫...
2025-05-01 18:46:17,138 [INFO] 处理文件: new_data\futures_um\trades\missing_pairs_20250501_180827.txt
2025-05-01 18:46:17,139 [INFO] 尝试直接从文件内容中提取币对: new_data\futures_um\trades\missing_pairs_20250501_180827.txt
2025-05-01 18:46:17,140 [WARNING] 忽略无效币对格式: ֤ʱ: 2025-05-01 18:08:27
2025-05-01 18:46:17,141 [WARNING] 忽略无效币对格式: г: futures/um
2025-05-01 18:46:17,141 [WARNING] 忽略无效币对格式: : trades
2025-05-01 18:46:17,142 [WARNING] 忽略无效币对格式: Ƶ: monthly
2025-05-01 18:46:17,142 [WARNING] 忽略无效币对格式: PUMPUSDT
2025-05-01 18:46:17,143 [INFO] 从文件中解析出 20 个缺失的币对: 无时间周期
2025-05-01 18:46:17,143 [INFO]   1. AIOTUSDT
2025-05-01 18:46:17,144 [INFO]   2. ATHUSDT
2025-05-01 18:46:17,144 [INFO]   3. BABYUSDT
2025-05-01 18:46:17,145 [INFO]   4. BANKUSDT
2025-05-01 18:46:17,145 [INFO]   5. DEEPUSDT
2025-05-01 18:46:17,146 [INFO]   6. EPTUSDT
2025-05-01 18:46:17,146 [INFO]   7. FHEUSDT
2025-05-01 18:46:17,147 [INFO]   8. FISUSDT
2025-05-01 18:46:17,147 [INFO]   9. FORTHUSDT
2025-05-01 18:46:17,147 [INFO]   10. HYPERUSDT
2025-05-01 18:46:17,148 [INFO]   ... 及更多 10 个币对
2025-05-01 18:46:17,148 [INFO] 使用市场类型: futures/um
2025-05-01 18:46:17,148 [INFO] 使用数据类型: trades
2025-05-01 18:46:17,149 [INFO] 使用数据频率: monthly
2025-05-01 18:46:17,149 [INFO] 处理缺失的币对
2025-05-01 18:46:17,150 [INFO] 启动Chrome浏览器...
2025-05-01 18:46:21,413 [INFO] 成功加载现有结果文件，包含 567 个币对
2025-05-01 18:46:21,414 [INFO] 开始补充爬取 20 个缺失的币对数据...
2025-05-01 18:46:21,414 [INFO] 处理币对 1/20: AIOTUSDT
2025-05-01 18:46:21,414 [INFO] 正在访问 AIOTUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/AIOTUSDT/
2025-05-01 18:46:21,415 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/AIOTUSDT/
2025-05-01 18:46:22,742 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:46:32,744 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:46:32,754 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:46:32,755 [INFO]   成功获取 AIOTUSDT 的 1 个数据文件
2025-05-01 18:46:32,755 [INFO] 处理币对 2/20: ATHUSDT
2025-05-01 18:46:32,756 [INFO] 正在访问 ATHUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/ATHUSDT/
2025-05-01 18:46:32,756 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/ATHUSDT/
2025-05-01 18:46:33,160 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:46:43,161 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:46:43,176 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:46:43,177 [INFO]   成功获取 ATHUSDT 的 1 个数据文件
2025-05-01 18:46:43,177 [INFO] 处理币对 3/20: BABYUSDT
2025-05-01 18:46:43,178 [INFO] 正在访问 BABYUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/BABYUSDT/
2025-05-01 18:46:43,179 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/BABYUSDT/
2025-05-01 18:46:43,588 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:46:53,588 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:46:53,602 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:46:53,602 [INFO]   成功获取 BABYUSDT 的 1 个数据文件
2025-05-01 18:46:53,603 [INFO] 处理币对 4/20: BANKUSDT
2025-05-01 18:46:53,604 [INFO] 正在访问 BANKUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/BANKUSDT/
2025-05-01 18:46:53,605 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/BANKUSDT/
2025-05-01 18:46:54,024 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:47:04,025 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:47:04,039 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:47:04,040 [INFO]   成功获取 BANKUSDT 的 1 个数据文件
2025-05-01 18:47:04,040 [INFO] 处理币对 5/20: DEEPUSDT
2025-05-01 18:47:04,041 [INFO] 正在访问 DEEPUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/DEEPUSDT/
2025-05-01 18:47:04,042 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/DEEPUSDT/
2025-05-01 18:47:04,300 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:47:14,301 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:47:14,315 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:47:14,316 [INFO]   成功获取 DEEPUSDT 的 1 个数据文件
2025-05-01 18:47:14,354 [INFO] 结果已保存到: binance_links\futures_um_monthly_trades_all_links.txt
2025-05-01 18:47:14,355 [INFO] 处理币对 6/20: EPTUSDT
2025-05-01 18:47:14,355 [INFO] 正在访问 EPTUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/EPTUSDT/
2025-05-01 18:47:14,356 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/EPTUSDT/
2025-05-01 18:47:14,773 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:47:24,774 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:47:24,788 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:47:24,789 [INFO]   成功获取 EPTUSDT 的 1 个数据文件
2025-05-01 18:47:24,789 [INFO] 处理币对 7/20: FHEUSDT
2025-05-01 18:47:24,790 [INFO] 正在访问 FHEUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/FHEUSDT/
2025-05-01 18:47:24,791 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/FHEUSDT/
2025-05-01 18:47:25,048 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:47:35,049 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:47:35,064 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:47:35,064 [INFO]   成功获取 FHEUSDT 的 1 个数据文件
2025-05-01 18:47:35,065 [INFO] 处理币对 8/20: FISUSDT
2025-05-01 18:47:35,066 [INFO] 正在访问 FISUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/FISUSDT/
2025-05-01 18:47:35,067 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/FISUSDT/
2025-05-01 18:47:35,333 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:47:45,335 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:47:45,347 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:47:45,348 [INFO]   成功获取 FISUSDT 的 1 个数据文件
2025-05-01 18:47:45,348 [INFO] 处理币对 9/20: FORTHUSDT
2025-05-01 18:47:45,349 [INFO] 正在访问 FORTHUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/FORTHUSDT/
2025-05-01 18:47:45,349 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/FORTHUSDT/
2025-05-01 18:47:45,605 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:47:55,606 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:47:55,620 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:47:55,620 [INFO]   成功获取 FORTHUSDT 的 1 个数据文件
2025-05-01 18:47:55,621 [INFO] 处理币对 10/20: HYPERUSDT
2025-05-01 18:47:55,622 [INFO] 正在访问 HYPERUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/HYPERUSDT/
2025-05-01 18:47:55,623 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/HYPERUSDT/
2025-05-01 18:47:56,021 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:48:06,023 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:48:06,033 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:48:06,034 [INFO]   成功获取 HYPERUSDT 的 1 个数据文件
2025-05-01 18:48:06,056 [INFO] 结果已保存到: binance_links\futures_um_monthly_trades_all_links.txt
2025-05-01 18:48:06,056 [INFO] 处理币对 11/20: INITUSDT
2025-05-01 18:48:06,057 [INFO] 正在访问 INITUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/INITUSDT/
2025-05-01 18:48:06,057 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/INITUSDT/
2025-05-01 18:48:06,303 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:48:16,304 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:48:16,318 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:48:16,319 [INFO]   成功获取 INITUSDT 的 1 个数据文件
2025-05-01 18:48:16,320 [INFO] 处理币对 12/20: JSTUSDT
2025-05-01 18:48:16,321 [INFO] 正在访问 JSTUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/JSTUSDT/
2025-05-01 18:48:16,321 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/JSTUSDT/
2025-05-01 18:48:16,588 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:48:26,588 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:48:26,603 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:48:26,603 [INFO]   成功获取 JSTUSDT 的 1 个数据文件
2025-05-01 18:48:26,604 [INFO] 处理币对 13/20: KERNELUSDT
2025-05-01 18:48:26,605 [INFO] 正在访问 KERNELUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/KERNELUSDT/
2025-05-01 18:48:26,606 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/KERNELUSDT/
2025-05-01 18:48:27,004 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:48:37,005 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:48:37,019 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:48:37,020 [INFO]   成功获取 KERNELUSDT 的 1 个数据文件
2025-05-01 18:48:37,021 [INFO] 处理币对 14/20: MEMEFIUSDT
2025-05-01 18:48:37,021 [INFO] 正在访问 MEMEFIUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/MEMEFIUSDT/
2025-05-01 18:48:37,022 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/MEMEFIUSDT/
2025-05-01 18:48:37,289 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:48:47,291 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:48:47,305 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:48:47,306 [INFO]   成功获取 MEMEFIUSDT 的 1 个数据文件
2025-05-01 18:48:47,307 [INFO] 处理币对 15/20: PROMPTUSDT
2025-05-01 18:48:47,308 [INFO] 正在访问 PROMPTUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/PROMPTUSDT/
2025-05-01 18:48:47,308 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/PROMPTUSDT/
2025-05-01 18:48:47,557 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:48:57,558 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:48:57,572 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:48:57,573 [INFO]   成功获取 PROMPTUSDT 的 1 个数据文件
2025-05-01 18:48:57,612 [INFO] 结果已保存到: binance_links\futures_um_monthly_trades_all_links.txt
2025-05-01 18:48:57,612 [INFO] 处理币对 16/20: PUNDIXUSDT
2025-05-01 18:48:57,613 [INFO] 正在访问 PUNDIXUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/PUNDIXUSDT/
2025-05-01 18:48:57,613 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/PUNDIXUSDT/
2025-05-01 18:48:57,865 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:49:07,867 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:49:07,881 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:49:07,882 [INFO]   成功获取 PUNDIXUSDT 的 1 个数据文件
2025-05-01 18:49:07,882 [INFO] 处理币对 17/20: SIGNUSDT
2025-05-01 18:49:07,883 [INFO] 正在访问 SIGNUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/SIGNUSDT/
2025-05-01 18:49:07,884 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/SIGNUSDT/
2025-05-01 18:49:08,286 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:49:18,288 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:49:18,302 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:49:18,302 [INFO]   成功获取 SIGNUSDT 的 1 个数据文件
2025-05-01 18:49:18,303 [INFO] 处理币对 18/20: STOUSDT
2025-05-01 18:49:18,304 [INFO] 正在访问 STOUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/STOUSDT/
2025-05-01 18:49:18,305 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/STOUSDT/
2025-05-01 18:49:19,250 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:49:29,252 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:49:29,266 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:49:29,267 [INFO]   成功获取 STOUSDT 的 1 个数据文件
2025-05-01 18:49:29,268 [INFO] 处理币对 19/20: WCTUSDT
2025-05-01 18:49:29,268 [INFO] 正在访问 WCTUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/WCTUSDT/
2025-05-01 18:49:29,269 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/WCTUSDT/
2025-05-01 18:49:29,719 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:49:39,721 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:49:39,736 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:49:39,736 [INFO]   成功获取 WCTUSDT 的 1 个数据文件
2025-05-01 18:49:39,737 [INFO] 处理币对 20/20: XCNUSDT
2025-05-01 18:49:39,738 [INFO] 正在访问 XCNUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/XCNUSDT/
2025-05-01 18:49:39,739 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/XCNUSDT/
2025-05-01 18:49:40,146 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 18:49:50,147 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 18:49:50,161 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 18:49:50,162 [INFO]   成功获取 XCNUSDT 的 1 个数据文件
2025-05-01 18:49:50,201 [INFO] 结果已保存到: binance_links\futures_um_monthly_trades_all_links.txt
2025-05-01 18:49:50,202 [INFO] 
补充爬取完成
2025-05-01 18:49:50,203 [INFO] 成功获取数据的币对: 20/20
2025-05-01 18:49:50,203 [INFO] 成功获取数据的币对列表:
2025-05-01 18:49:50,203 [INFO]   - AIOTUSDT (1 个文件)
2025-05-01 18:49:50,204 [INFO]   - ATHUSDT (1 个文件)
2025-05-01 18:49:50,204 [INFO]   - BABYUSDT (1 个文件)
2025-05-01 18:49:50,205 [INFO]   - BANKUSDT (1 个文件)
2025-05-01 18:49:50,205 [INFO]   - DEEPUSDT (1 个文件)
2025-05-01 18:49:50,206 [INFO]   - EPTUSDT (1 个文件)
2025-05-01 18:49:50,206 [INFO]   - FHEUSDT (1 个文件)
2025-05-01 18:49:50,207 [INFO]   - FISUSDT (1 个文件)
2025-05-01 18:49:50,207 [INFO]   - FORTHUSDT (1 个文件)
2025-05-01 18:49:50,207 [INFO]   - HYPERUSDT (1 个文件)
2025-05-01 18:49:50,208 [INFO]   - INITUSDT (1 个文件)
2025-05-01 18:49:50,208 [INFO]   - JSTUSDT (1 个文件)
2025-05-01 18:49:50,209 [INFO]   - KERNELUSDT (1 个文件)
2025-05-01 18:49:50,209 [INFO]   - MEMEFIUSDT (1 个文件)
2025-05-01 18:49:50,210 [INFO]   - PROMPTUSDT (1 个文件)
2025-05-01 18:49:50,210 [INFO]   - PUNDIXUSDT (1 个文件)
2025-05-01 18:49:50,211 [INFO]   - SIGNUSDT (1 个文件)
2025-05-01 18:49:50,211 [INFO]   - STOUSDT (1 个文件)
2025-05-01 18:49:50,212 [INFO]   - WCTUSDT (1 个文件)
2025-05-01 18:49:50,212 [INFO]   - XCNUSDT (1 个文件)
2025-05-01 18:49:50,217 [INFO] 已保存新增数据到: new_data\futures_um\trades\new_data_20250501.txt
2025-05-01 18:49:50,217 [INFO] 总共获取: 20个币对, 20个链接
2025-05-01 18:49:50,218 [INFO] 补充爬取完成，新增数据已保存到: new_data\futures_um\trades\new_data_20250501.txt
2025-05-01 18:49:50,220 [INFO] 已标记文件为已处理: new_data\futures_um\trades\missing_pairs_20250501_180827.txt.processed
2025-05-01 18:49:52,963 [INFO] 浏览器已关闭
