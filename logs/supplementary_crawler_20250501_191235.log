2025-05-01 19:12:35,462 [INFO] 开始运行币安数据补充爬虫...
2025-05-01 19:12:35,463 [INFO] 处理文件: new_data\futures_um\metrics\missing_pairs_20250501_191036.txt
2025-05-01 19:12:35,464 [INFO] 尝试直接从文件内容中提取币对: new_data\futures_um\metrics\missing_pairs_20250501_191036.txt
2025-05-01 19:12:35,464 [WARNING] 忽略无效币对格式: ֤ʱ: 2025-05-01 19:10:36
2025-05-01 19:12:35,465 [WARNING] 忽略无效币对格式: г: futures/um
2025-05-01 19:12:35,465 [WARNING] 忽略无效币对格式: : metrics
2025-05-01 19:12:35,466 [WARNING] 忽略无效币对格式: Ƶ: daily
2025-05-01 19:12:35,467 [INFO] 从文件中解析出 4 个缺失的币对: 无时间周期
2025-05-01 19:12:35,467 [INFO]   1. AIOTUSDT
2025-05-01 19:12:35,468 [INFO]   2. JSTUSDT
2025-05-01 19:12:35,468 [INFO]   3. PUNDIXUSDT
2025-05-01 19:12:35,469 [INFO]   4. SIGNUSDT
2025-05-01 19:12:35,469 [INFO] 未能解析数据频率，使用默认值: monthly
2025-05-01 19:12:35,469 [INFO] 使用命令行指定的数据频率覆盖文件中的值: daily
2025-05-01 19:12:35,470 [INFO] 使用市场类型: futures/um
2025-05-01 19:12:35,470 [INFO] 使用数据类型: metrics
2025-05-01 19:12:35,471 [INFO] 使用数据频率: daily
2025-05-01 19:12:35,471 [INFO] 处理缺失的币对
2025-05-01 19:12:35,471 [INFO] 启动Chrome浏览器...
2025-05-01 19:12:38,964 [INFO] 成功加载现有结果文件，包含 570 个币对
2025-05-01 19:12:38,971 [INFO] 开始补充爬取 4 个缺失的币对数据...
2025-05-01 19:12:38,971 [INFO] 处理币对 1/4: AIOTUSDT
2025-05-01 19:12:38,972 [INFO] 正在访问 AIOTUSDT 的 metrics 数据页面: https://data.binance.vision/?prefix=data/futures/um/daily/metrics/AIOTUSDT/
2025-05-01 19:12:38,972 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/daily/metrics/AIOTUSDT/
2025-05-01 19:12:40,115 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 19:12:50,116 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 19:12:50,132 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 19:12:50,133 [INFO]   成功获取 AIOTUSDT 的 1 个数据文件
2025-05-01 19:12:50,133 [INFO] 处理币对 2/4: JSTUSDT
2025-05-01 19:12:50,134 [INFO] 正在访问 JSTUSDT 的 metrics 数据页面: https://data.binance.vision/?prefix=data/futures/um/daily/metrics/JSTUSDT/
2025-05-01 19:12:50,135 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/daily/metrics/JSTUSDT/
2025-05-01 19:12:50,428 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 19:13:00,429 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 19:13:00,443 [INFO]   使用JavaScript方法找到 3 个数据文件
2025-05-01 19:13:00,444 [INFO]   成功获取 JSTUSDT 的 3 个数据文件
2025-05-01 19:13:00,444 [INFO] 处理币对 3/4: PUNDIXUSDT
2025-05-01 19:13:00,445 [INFO] 正在访问 PUNDIXUSDT 的 metrics 数据页面: https://data.binance.vision/?prefix=data/futures/um/daily/metrics/PUNDIXUSDT/
2025-05-01 19:13:00,446 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/daily/metrics/PUNDIXUSDT/
2025-05-01 19:13:00,730 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 19:13:10,731 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 19:13:10,745 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 19:13:10,746 [INFO]   成功获取 PUNDIXUSDT 的 1 个数据文件
2025-05-01 19:13:10,746 [INFO] 处理币对 4/4: SIGNUSDT
2025-05-01 19:13:10,747 [INFO] 正在访问 SIGNUSDT 的 metrics 数据页面: https://data.binance.vision/?prefix=data/futures/um/daily/metrics/SIGNUSDT/
2025-05-01 19:13:10,748 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/daily/metrics/SIGNUSDT/
2025-05-01 19:13:11,024 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 19:13:21,025 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 19:13:21,037 [INFO]   使用JavaScript方法找到 3 个数据文件
2025-05-01 19:13:21,038 [INFO]   成功获取 SIGNUSDT 的 3 个数据文件
2025-05-01 19:13:21,575 [INFO] 结果已保存到: binance_links\futures_um_daily_metrics_all_links.txt
2025-05-01 19:13:21,576 [INFO] 
补充爬取完成
2025-05-01 19:13:21,576 [INFO] 成功获取数据的币对: 4/4
2025-05-01 19:13:21,577 [INFO] 成功获取数据的币对列表:
2025-05-01 19:13:21,578 [INFO]   - AIOTUSDT (1 个文件)
2025-05-01 19:13:21,578 [INFO]   - JSTUSDT (3 个文件)
2025-05-01 19:13:21,579 [INFO]   - PUNDIXUSDT (1 个文件)
2025-05-01 19:13:21,579 [INFO]   - SIGNUSDT (3 个文件)
2025-05-01 19:13:21,584 [INFO] 已保存新增数据到: new_data\futures_um\metrics\new_data_20250501.txt
2025-05-01 19:13:21,584 [INFO] 总共获取: 4个币对, 8个链接
2025-05-01 19:13:21,585 [INFO] 补充爬取完成，新增数据已保存到: new_data\futures_um\metrics\new_data_20250501.txt
2025-05-01 19:13:21,587 [INFO] 已标记文件为已处理: new_data\futures_um\metrics\missing_pairs_20250501_191036.txt.processed
2025-05-01 19:13:24,208 [INFO] 浏览器已关闭
