2025-05-01 19:13:29,495 [INFO] 开始运行币安数据补充爬虫...
2025-05-01 19:13:29,496 [INFO] 处理文件: new_data\futures_um\metrics\zero_data_pairs_20250501_191036.txt
2025-05-01 19:13:29,496 [INFO] 检测到零数据币对文件
2025-05-01 19:13:29,497 [INFO] 尝试直接从文件内容中提取币对: new_data\futures_um\metrics\zero_data_pairs_20250501_191036.txt
2025-05-01 19:13:29,498 [WARNING] 忽略无效币对格式: ֤ʱ: 2025-05-01 19:10:36
2025-05-01 19:13:29,498 [WARNING] 忽略无效币对格式: г: futures/um
2025-05-01 19:13:29,499 [WARNING] 忽略无效币对格式: : metrics
2025-05-01 19:13:29,500 [WARNING] 忽略无效币对格式: Ƶ: daily
2025-05-01 19:13:29,500 [INFO] 从文件中解析出 1 个零数据币对: 无时间周期
2025-05-01 19:13:29,501 [INFO]   1. COMPUSDT
2025-05-01 19:13:29,501 [INFO] 未能解析数据频率，使用默认值: monthly
2025-05-01 19:13:29,502 [INFO] 使用命令行指定的数据频率覆盖文件中的值: daily
2025-05-01 19:13:29,502 [INFO] 使用市场类型: futures/um
2025-05-01 19:13:29,503 [INFO] 使用数据类型: metrics
2025-05-01 19:13:29,503 [INFO] 使用数据频率: daily
2025-05-01 19:13:29,503 [INFO] 处理零数据币对
2025-05-01 19:13:29,504 [INFO] 启动Chrome浏览器...
2025-05-01 19:13:32,851 [INFO] 成功加载现有结果文件，包含 574 个币对
2025-05-01 19:13:32,859 [INFO] 开始补充爬取 1 个缺失的币对数据...
2025-05-01 19:13:32,859 [INFO] 处理币对 1/1: COMPUSDT
2025-05-01 19:13:32,859 [INFO] 正在访问 COMPUSDT 的 metrics 数据页面: https://data.binance.vision/?prefix=data/futures/um/daily/metrics/COMPUSDT/
2025-05-01 19:13:32,860 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/daily/metrics/COMPUSDT/
2025-05-01 19:13:34,150 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 19:13:44,152 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 19:13:44,225 [INFO]   使用JavaScript方法找到 1247 个数据文件
2025-05-01 19:13:44,226 [INFO]   成功获取 COMPUSDT 的 1247 个数据文件
2025-05-01 19:13:44,695 [INFO] 结果已保存到: binance_links\futures_um_daily_metrics_all_links.txt
2025-05-01 19:13:44,696 [INFO] 
补充爬取完成
2025-05-01 19:13:44,697 [INFO] 成功获取数据的币对: 1/1
2025-05-01 19:13:44,698 [INFO] 成功获取数据的币对列表:
2025-05-01 19:13:44,699 [INFO]   - COMPUSDT (1247 个文件)
2025-05-01 19:13:44,703 [INFO] 已保存新增数据到: new_data\futures_um\metrics\new_data_20250501.txt
2025-05-01 19:13:44,704 [INFO] 总共获取: 1个币对, 1247个链接
2025-05-01 19:13:44,704 [INFO] 补充爬取完成，新增数据已保存到: new_data\futures_um\metrics\new_data_20250501.txt
2025-05-01 19:13:44,706 [INFO] 已标记文件为已处理: new_data\futures_um\metrics\zero_data_pairs_20250501_191036.txt.processed
2025-05-01 19:13:47,296 [INFO] 浏览器已关闭
