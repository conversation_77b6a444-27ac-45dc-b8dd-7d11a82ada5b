2025-05-01 19:26:27,203 [INFO] 开始运行币安数据补充爬虫...
2025-05-01 19:26:27,204 [INFO] 处理文件: new_data\futures_um\trades\missing_pairs_20250501_192559.txt
2025-05-01 19:26:27,204 [INFO] 尝试直接从文件内容中提取币对: new_data\futures_um\trades\missing_pairs_20250501_192559.txt
2025-05-01 19:26:27,205 [WARNING] 忽略无效币对格式: ֤ʱ: 2025-05-01 19:25:59
2025-05-01 19:26:27,206 [WARNING] 忽略无效币对格式: г: futures/um
2025-05-01 19:26:27,206 [WARNING] 忽略无效币对格式: : trades
2025-05-01 19:26:27,207 [WARNING] 忽略无效币对格式: Ƶ: monthly
2025-05-01 19:26:27,208 [INFO] 从文件中解析出 1 个缺失的币对: 无时间周期
2025-05-01 19:26:27,208 [INFO]   1. PUMPUSDT
2025-05-01 19:26:27,209 [INFO] 未能解析数据频率，使用默认值: monthly
2025-05-01 19:26:27,209 [INFO] 使用市场类型: futures/um
2025-05-01 19:26:27,209 [INFO] 使用数据类型: trades
2025-05-01 19:26:27,210 [INFO] 使用数据频率: monthly
2025-05-01 19:26:27,210 [INFO] 处理缺失的币对
2025-05-01 19:26:27,211 [INFO] 启动Chrome浏览器...
2025-05-01 19:26:30,462 [INFO] 成功加载现有结果文件，包含 587 个币对
2025-05-01 19:26:30,463 [INFO] 开始补充爬取 1 个缺失的币对数据...
2025-05-01 19:26:30,464 [INFO] 处理币对 1/1: PUMPUSDT
2025-05-01 19:26:30,464 [INFO] 正在访问 PUMPUSDT 的 trades 数据页面: https://data.binance.vision/?prefix=data/futures/um/monthly/trades/PUMPUSDT/
2025-05-01 19:26:30,464 [INFO] 尝试加载页面 (尝试 1/5): https://data.binance.vision/?prefix=data/futures/um/monthly/trades/PUMPUSDT/
2025-05-01 19:26:32,203 [INFO] 页面已加载，等待10秒确保内容完全渲染...
2025-05-01 19:26:42,204 [INFO]   尝试使用JavaScript方法获取数据文件...
2025-05-01 19:26:42,221 [INFO]   使用JavaScript方法找到 1 个数据文件
2025-05-01 19:26:42,221 [INFO]   成功获取 PUMPUSDT 的 1 个数据文件
2025-05-01 19:26:42,257 [INFO] 结果已保存到: binance_links\futures_um_monthly_trades_all_links.txt
2025-05-01 19:26:42,257 [INFO] 
补充爬取完成
2025-05-01 19:26:42,258 [INFO] 成功获取数据的币对: 1/1
2025-05-01 19:26:42,259 [INFO] 成功获取数据的币对列表:
2025-05-01 19:26:42,260 [INFO]   - PUMPUSDT (1 个文件)
2025-05-01 19:26:42,269 [INFO] 已保存新增数据到: new_data\futures_um\trades\new_data_20250501.txt
2025-05-01 19:26:42,269 [INFO] 总共获取: 1个币对, 1个链接
2025-05-01 19:26:42,270 [INFO] 补充爬取完成，新增数据已保存到: new_data\futures_um\trades\new_data_20250501.txt
2025-05-01 19:26:42,274 [INFO] 已标记文件为已处理: new_data\futures_um\trades\missing_pairs_20250501_192559.txt.processed
2025-05-01 19:26:44,889 [INFO] 浏览器已关闭
