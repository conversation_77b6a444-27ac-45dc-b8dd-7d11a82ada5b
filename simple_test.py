#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re

# 重复文件名模式（正则表达式）
duplicate_patterns = [
    r'\(\d+\)$',  # 匹配 "(1)", "(2)" 等
    r'副本$',
    r'复制$',
    r'copy$',
    r'_\d+$',  # 匹配 "_1", "_2" 等
]

def is_duplicate_filename(original, potential_duplicate):
    """检查文件名是否是原始文件的副本"""
    # 获取文件名和扩展名
    original_name, original_ext = os.path.splitext(original)
    dup_name, dup_ext = os.path.splitext(potential_duplicate)

    # 扩展名必须相同
    if original_ext.lower() != dup_ext.lower():
        return False

    # 检查是否原始文件名是潜在副本的前缀
    if not dup_name.startswith(original_name):
        return False

    # 检查后缀是否匹配任何重复模式
    suffix = dup_name[len(original_name):]
    if not suffix:
        return False

    for pattern in duplicate_patterns:
        if re.search(pattern, suffix):
            return True

    return False

def test_cross_folder_comparison():
    """测试跨文件夹对比功能"""
    print("=" * 50)
    print("测试跨文件夹重复文件检测功能")
    print("=" * 50)
    
    # 扫描测试目录
    scan_directories = ['H:\\binance_crawler']
    all_files = {}  # 格式: {size: {name: [paths]}}
    
    for directory in scan_directories:
        if not os.path.exists(directory):
            print(f"目录不存在: {directory}")
            continue
            
        print(f"扫描目录: {directory}")
        
        for root, _, files in os.walk(directory):
            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    file_name = os.path.basename(file_path)
                    
                    if file_size not in all_files:
                        all_files[file_size] = {}
                    
                    # 跨文件夹模式：所有文件放在一起比较
                    if file_name not in all_files[file_size]:
                        all_files[file_size][file_name] = []
                    all_files[file_size][file_name].append(file_path)
                    
                except Exception as e:
                    print(f"处理文件时出错: {file_path}, 错误: {str(e)}")
    
    print(f"\n收集到的文件信息:")
    total_files = sum(len(paths) for names in all_files.values() for paths in names.values())
    print(f"按文件大小分组后，共有 {len(all_files)} 个不同大小的文件组，总计 {total_files} 个文件")
    
    # 查找重复文件
    duplicate_groups = []
    sizes_with_duplicates = [size for size, names in all_files.items() if len(names) > 1]
    
    print(f"\n找到 {len(sizes_with_duplicates)} 个可能包含重复文件的大小组")
    
    for size in sizes_with_duplicates:
        size_files = all_files[size]
        file_names = list(size_files.keys())
        
        print(f"\n检查大小为 {size} 字节的文件组，包含 {len(file_names)} 个不同文件名:")
        for name in file_names:
            print(f"  文件名: {name}, 路径数: {len(size_files[name])}")
            for path in size_files[name]:
                print(f"    路径: {path}")
        
        for i in range(len(file_names)):
            for j in range(i+1, len(file_names)):
                name1 = file_names[i]
                name2 = file_names[j]
                
                paths1 = size_files[name1]
                paths2 = size_files[name2]

                # 检查是否是重复文件名模式
                is_dup_1_2 = is_duplicate_filename(name1, name2)  # name2是name1的副本
                is_dup_2_1 = is_duplicate_filename(name2, name1)  # name1是name2的副本
                is_same_name = (name1 == name2)  # 完全相同的文件名
                
                print(f"\n比较文件名: '{name1}' vs '{name2}'")
                print(f"  is_dup_1_2: {is_dup_1_2}, is_dup_2_1: {is_dup_2_1}, is_same_name: {is_same_name}")
                
                if is_dup_1_2 or is_dup_2_1 or is_same_name:
                    # 对于每个路径组合进行检查
                    for path1 in paths1:
                        for path2 in paths2:
                            # 确保不是同一个文件
                            if path1 != path2:
                                # 确保在不同文件夹中（跨文件夹对比的核心）
                                dir1 = os.path.dirname(path1)
                                dir2 = os.path.dirname(path2)
                                
                                print(f"  比较路径: '{path1}' vs '{path2}'")
                                print(f"    目录1: {dir1}")
                                print(f"    目录2: {dir2}")
                                print(f"    不同目录: {dir1 != dir2}")
                                
                                if dir1 != dir2:
                                    if is_dup_1_2:
                                        duplicate_groups.append((path1, path2))  # path2是副本
                                        print(f"    ✓ 添加重复文件组: 原始='{path1}', 副本='{path2}'")
                                    elif is_dup_2_1:
                                        duplicate_groups.append((path2, path1))  # path1是副本
                                        print(f"    ✓ 添加重复文件组: 原始='{path2}', 副本='{path1}'")
                                    elif is_same_name:
                                        # 相同文件名，选择路径较短的作为原始文件
                                        if len(path1) <= len(path2):
                                            duplicate_groups.append((path1, path2))
                                            print(f"    ✓ 添加重复文件组(相同名称): 原始='{path1}', 副本='{path2}'")
                                        else:
                                            duplicate_groups.append((path2, path1))
                                            print(f"    ✓ 添加重复文件组(相同名称): 原始='{path2}', 副本='{path1}'")
    
    # 显示结果
    print(f"\n" + "=" * 50)
    print(f"检测结果:")
    print(f"=" * 50)
    if duplicate_groups:
        print(f"找到 {len(duplicate_groups)} 组重复文件:")
        for i, (original, duplicate) in enumerate(duplicate_groups):
            print(f"{i+1}. 原始文件: {original}")
            print(f"   重复文件: {duplicate}")
    else:
        print("未找到重复文件")

if __name__ == "__main__":
    test_cross_folder_comparison()
