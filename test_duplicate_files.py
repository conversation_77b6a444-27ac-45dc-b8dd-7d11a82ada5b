#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重复文件查找测试脚本
用于测试跨文件夹对比功能
"""

import os
import shutil
import tempfile
from find_duplicate_files import CONFIG, find_duplicates

def create_test_environment():
    """创建测试环境"""
    # 创建临时测试目录
    test_base = "test_duplicate_detection"
    if os.path.exists(test_base):
        shutil.rmtree(test_base)
    
    # 创建测试目录结构
    folder1 = os.path.join(test_base, "folder1")
    folder2 = os.path.join(test_base, "folder2")
    
    os.makedirs(folder1, exist_ok=True)
    os.makedirs(folder2, exist_ok=True)
    
    # 创建测试文件
    test_content = "这是一个测试文件的内容"
    
    # 在folder1中创建原始文件
    with open(os.path.join(folder1, "test_file.txt"), "w", encoding="utf-8") as f:
        f.write(test_content)
    
    # 在folder2中创建相同内容的文件（相同文件名）
    with open(os.path.join(folder2, "test_file.txt"), "w", encoding="utf-8") as f:
        f.write(test_content)
    
    # 在folder2中创建副本文件（带副本后缀）
    with open(os.path.join(folder2, "test_file(1).txt"), "w", encoding="utf-8") as f:
        f.write(test_content)
    
    # 在folder1中创建另一个副本文件
    with open(os.path.join(folder1, "test_file_复制.txt"), "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print(f"测试环境已创建在: {test_base}")
    print("文件结构:")
    print(f"  {folder1}/")
    print(f"    test_file.txt")
    print(f"    test_file_复制.txt")
    print(f"  {folder2}/")
    print(f"    test_file.txt")
    print(f"    test_file(1).txt")
    
    return test_base

def run_test():
    """运行测试"""
    print("=" * 50)
    print("开始测试跨文件夹重复文件检测功能")
    print("=" * 50)
    
    # 创建测试环境
    test_dir = create_test_environment()
    
    # 备份原始配置
    original_config = CONFIG.copy()
    
    try:
        # 修改配置为测试模式
        CONFIG['scan_directories'] = [test_dir]
        CONFIG['cross_folder_comparison'] = True
        CONFIG['auto_delete'] = False  # 测试模式不自动删除
        CONFIG['debug_mode'] = True
        CONFIG['verify_with_hash'] = False
        
        print("\n开始运行重复文件检测...")
        find_duplicates()
        
    finally:
        # 恢复原始配置
        CONFIG.clear()
        CONFIG.update(original_config)
        
        # 清理测试环境
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"\n测试环境已清理: {test_dir}")

if __name__ == "__main__":
    run_test()
