#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试文件名优先级逻辑
"""

import os
import shutil
from find_duplicate_files import CONFIG, find_duplicates

def create_priority_test():
    """创建优先级测试环境"""
    test_base = "test_filename_priority"
    if os.path.exists(test_base):
        shutil.rmtree(test_base)
    
    # 创建目录结构
    folders = [
        "重要目录/master",      # 优先保留目录
        "临时目录/temp",        # 优先删除目录
        "普通目录1",
        "普通目录2",
    ]
    
    for folder in folders:
        full_path = os.path.join(test_base, folder)
        os.makedirs(full_path, exist_ok=True)
    
    # 创建测试文件内容
    test_content = "这是测试文件的内容"
    
    # 创建测试场景
    test_files = [
        # 场景1: 文件名模式 vs 目录优先级
        # 应该删除副本文件，即使它在重要目录中
        ("重要目录/master/document.txt", test_content),
        ("临时目录/temp/document(1).txt", test_content),  # 应该删除这个（文件名模式优先）
        
        # 场景2: 明确的副本模式
        ("普通目录1/report.txt", test_content),
        ("普通目录2/report副本.txt", test_content),        # 应该删除这个
        
        # 场景3: 数字后缀副本
        ("普通目录1/data.xlsx", test_content),
        ("普通目录2/data_2.xlsx", test_content),          # 应该删除这个
        
        # 场景4: 括号副本
        ("普通目录1/video.mp4", test_content),
        ("普通目录2/video(1).mp4", test_content),         # 应该删除这个
        
        # 场景5: 复制后缀
        ("普通目录1/config.ini", test_content),
        ("普通目录2/config_复制.ini", test_content),       # 应该删除这个
        
        # 场景6: 相同文件名，应该根据目录优先级判断
        ("重要目录/master/same_name.txt", test_content),  # 应该保留（重要目录）
        ("临时目录/temp/same_name.txt", test_content),    # 应该删除（临时目录）
        
        # 场景7: 宽松模式测试（你提到的问题）
        ("普通目录1/01_file.txt", test_content),
        ("普通目录2/01.txt", test_content),               # 应该删除这个（宽松模式）
    ]
    
    for file_path, content in test_files:
        full_path = os.path.join(test_base, file_path)
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(content)
    
    print(f"优先级测试环境已创建在: {test_base}")
    print("测试场景:")
    print("1. 文件名模式 vs 目录优先级 → 文件名模式应该获胜")
    print("2. 明确的副本模式 → 应该删除副本文件")
    print("3. 相同文件名 → 应该根据目录优先级判断")
    print("4. 宽松模式 → 应该识别 01_file.txt vs 01.txt")
    
    return test_base

def run_test():
    """运行测试"""
    print("=" * 70)
    print("测试文件名优先级逻辑")
    print("=" * 70)
    
    # 创建测试环境
    test_dir = create_priority_test()
    
    # 备份原始配置
    original_config = CONFIG.copy()
    
    try:
        # 修改配置为测试模式
        CONFIG['scan_directories'] = [test_dir]
        CONFIG['cross_folder_comparison'] = True
        CONFIG['debug_mode'] = False  # 关闭调试模式，看清楚结果
        CONFIG['verify_with_hash'] = False
        CONFIG['file_selection_strategy']['show_selection_reason'] = True
        CONFIG['delete_mode']['mode'] = 'smart_auto'  # 使用智能自动模式
        
        # 确保宽松模式启用
        CONFIG['duplicate_patterns']['enable_loose_mode'] = True
        CONFIG['duplicate_patterns']['loose_mode_require_hash'] = False  # 测试时不要求哈希
        
        print(f"\n当前配置:")
        print(f"  删除模式: {CONFIG['delete_mode']['mode']}")
        print(f"  启用宽松模式: {CONFIG['duplicate_patterns']['enable_loose_mode']}")
        print(f"  智能自动删除模式: {CONFIG['delete_mode']['smart_auto_patterns']}")
        
        print(f"\n开始运行重复文件检测...")
        print("预期结果:")
        print("  - document(1).txt 应该被自动删除（文件名模式优先）")
        print("  - report副本.txt 应该被自动删除")
        print("  - data_2.xlsx 应该被自动删除")
        print("  - video(1).mp4 应该被自动删除")
        print("  - config_复制.ini 应该被自动删除")
        print("  - temp目录中的same_name.txt 应该被自动删除（目录优先级）")
        print("  - 01.txt 应该被识别并处理（宽松模式）")
        
        find_duplicates()
        
    finally:
        # 恢复原始配置
        CONFIG.clear()
        CONFIG.update(original_config)
        
        # 询问是否清理测试环境
        cleanup = input(f"\n是否清理测试环境 {test_dir}? (y/n): ").strip().lower()
        if cleanup == 'y':
            shutil.rmtree(test_dir)
            print(f"测试环境已清理: {test_dir}")
        else:
            print(f"测试环境保留在: {test_dir}")

if __name__ == "__main__":
    run_test()
