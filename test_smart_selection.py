#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试智能文件选择功能
"""

import os
import shutil
import time
from find_duplicate_files import CONFIG, find_duplicates

def create_smart_selection_test():
    """创建智能选择测试环境"""
    test_base = "test_smart_selection"
    if os.path.exists(test_base):
        shutil.rmtree(test_base)
    
    # 创建复杂的目录结构来测试不同的选择策略
    folders = [
        "原始文件/重要数据",           # 优先保留目录
        "backup/数据备份",            # 备份目录
        "temp/临时文件",              # 临时目录（优先删除）
        "下载/新下载",                # 下载目录（优先删除）
        "master/主要文件",            # 主要目录（优先保留）
        "副本/复制文件",              # 副本目录（优先删除）
        "深层/目录/结构/很深/的/路径", # 深层目录
        "浅层目录",                   # 浅层目录
    ]
    
    for folder in folders:
        full_path = os.path.join(test_base, folder)
        os.makedirs(full_path, exist_ok=True)
    
    # 创建测试文件内容
    test_content = "这是测试文件的内容"
    
    # 创建不同场景的测试文件
    test_files = [
        # 场景1: 优先保留目录 vs 普通目录
        ("原始文件/重要数据/document.txt", test_content, 0),
        ("浅层目录/document.txt", test_content, 0),
        
        # 场景2: 优先删除目录 vs 普通目录  
        ("temp/临时文件/report.txt", test_content, 0),
        ("浅层目录/report.txt", test_content, 0),
        
        # 场景3: 文件时间差异（较新 vs 较旧）
        ("backup/数据备份/data.xlsx", test_content, 0),      # 较旧
        ("浅层目录/data.xlsx", test_content, 120),           # 较新（2分钟后）
        
        # 场景4: 目录深度差异
        ("深层/目录/结构/很深/的/路径/deep_file.txt", test_content, 0),
        ("浅层目录/deep_file.txt", test_content, 0),
        
        # 场景5: 副本文件名模式
        ("master/主要文件/original.txt", test_content, 0),
        ("副本/复制文件/original(1).txt", test_content, 0),
        
        # 场景6: 多重因素冲突
        ("下载/新下载/conflict.txt", test_content, 180),     # 下载目录（优先删除）但较新
        ("深层/目录/结构/很深/的/路径/conflict.txt", test_content, 0),  # 深层目录但较旧
    ]
    
    for file_path, content, time_offset in test_files:
        full_path = os.path.join(test_base, file_path)
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        # 设置文件修改时间
        if time_offset > 0:
            new_time = time.time() + time_offset
            os.utime(full_path, (new_time, new_time))
    
    print(f"智能选择测试环境已创建在: {test_base}")
    print("测试场景:")
    print("1. 优先保留目录 vs 普通目录")
    print("2. 优先删除目录 vs 普通目录")
    print("3. 文件时间差异（较新 vs 较旧）")
    print("4. 目录深度差异")
    print("5. 副本文件名模式")
    print("6. 多重因素冲突")
    
    return test_base

def run_test():
    """运行测试"""
    print("=" * 70)
    print("测试智能文件选择功能")
    print("=" * 70)
    
    # 创建测试环境
    test_dir = create_smart_selection_test()
    
    # 备份原始配置
    original_config = CONFIG.copy()
    
    try:
        # 修改配置为测试模式
        CONFIG['scan_directories'] = [test_dir]
        CONFIG['cross_folder_comparison'] = True
        CONFIG['auto_delete'] = False
        CONFIG['debug_mode'] = False
        CONFIG['verify_with_hash'] = False
        CONFIG['file_selection_strategy']['show_selection_reason'] = True
        
        print(f"\n智能选择策略配置:")
        strategy = CONFIG['file_selection_strategy']
        print(f"  优先保留目录: {len(strategy['priority_directories'])} 个模式")
        print(f"  优先删除目录: {len(strategy['delete_priority_directories'])} 个模式")
        print(f"  偏好较新文件: {strategy['prefer_newer_files']}")
        print(f"  偏好浅层目录: {strategy['prefer_shallow_directories']}")
        print(f"  显示选择理由: {strategy['show_selection_reason']}")
        
        print(f"\n开始运行重复文件检测...")
        find_duplicates()
        
    finally:
        # 恢复原始配置
        CONFIG.clear()
        CONFIG.update(original_config)
        
        # 询问是否清理测试环境
        cleanup = input(f"\n是否清理测试环境 {test_dir}? (y/n): ").strip().lower()
        if cleanup == 'y':
            shutil.rmtree(test_dir)
            print(f"测试环境已清理: {test_dir}")
        else:
            print(f"测试环境保留在: {test_dir}")

if __name__ == "__main__":
    run_test()
